import React, { useState, useEffect } from 'react';
import {
  StyleSheet, View, ScrollView, TouchableOpacity, Image,
  RefreshControl, ActivityIndicator
} from 'react-native';
import ThemedText from '../../components/ThemedText';
import ThemedView from '../../components/ThemedView';
import ProfileSidebar from '../../components/ProfileSidebar';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import GuestPermissionModal from '../../components/GuestPermissionModal';
import { useGuestPermission } from '../../hooks/useGuestPermission';
import { postApi, authApi } from '../../lib/apiServices';
import PostCard from '../../components/PostCard';

const tabs = ['学习搭子', '自习室', '职通车', '互助站'];
const tabToPage = {
  '学习搭子': '/community-team',
  '自习室': '/community-study',
  '职通车': '/community-career',
  '互助站': '/community-friends',
};

const Community = () => {
  const router = useRouter();
  const params = useLocalSearchParams();

  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [posts, setPosts] = useState([]);
  const [filterTag, setFilterTag] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const {
    wrapGuestAction,
    showPermissionModal,
    currentFeature,
    handleRegister,
    closePermissionModal
  } = useGuestPermission();

  const fetchPosts = async (tag = null) => {
    try {
      setLoading(true);
      setError(null);

      // 1. 获取帖子列表
      const response = await postApi.getList(tag);
      const postsData = response.data || [];

      if (postsData.length === 0) {
        setPosts([]);
        return;
      }

      // 2. 提取所有唯一的userId
      const userIds = [...new Set(postsData.map(post => post.userId).filter(Boolean))];
      console.log('📋 提取到的用户ID列表:', userIds);

      // 3. 批量获取用户信息
      let usersMap = {};
      if (userIds.length > 0) {
        try {
          console.log('🔄 开始批量获取用户信息...');
          const users = await authApi.getUsersByIds(userIds);
          console.log('✅ 获取到的用户信息:', users);

          // 创建userId到用户信息的映射
          usersMap = users.reduce((map, user) => {
            console.log('👤 映射用户:', { userId: user.id, userAccount: user.userAccount, userName: user.userName });
            map[user.id] = user;
            return map;
          }, {});
          console.log('🗺️ 用户映射表:', usersMap);
        } catch (userError) {
          console.warn('❌ 获取用户信息失败，使用默认显示:', userError);
        }
      } else {
        console.log('⚠️ 没有找到有效的用户ID');
      }

      // 4. 格式化帖子数据，优先使用后端直接提供的 userName 和 userAvatar
      const formatted = postsData.map(post => ({
        ...post,
        id: post.id,
        title: post.title,
        content: post.content,
        imageUrls: post.imageUrls ? post.imageUrls.split(',').filter(Boolean) : [],
        tag: post.tag,
        likeCount: post.likeCount || 0,
        commentCount: post.commentCount || 0,
        viewCount: post.viewCount || 0,
        isHidden: post.isHidden || false,
        createTime: post.createTime,
        time: formatTime(post.createTime),
        userId: post.userId,

        // ✅ 直接使用后端返回字段
        user: post.userName || '匿名用户',
        userAvatar: post.userAvatar || null,

        likes: post.likeCount || 0,
        comments: post.commentCount || 0,
        hasMedia: post.imageUrls && post.imageUrls.trim().length > 0,
      }));


      setPosts(formatted);
    } catch (err) {
      setError(err.message || '获取帖子失败');
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (createTime) => {
    if (!createTime) return '未知时间';

    const createDate = new Date(createTime);
    const now = new Date();
    const diffMs = now.getTime() - createDate.getTime();

    // 如果时间差为负数（未来时间），显示刚刚
    if (diffMs < 0) return '刚刚';

    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);
    const diffWeeks = Math.floor(diffDays / 7);
    const diffMonths = Math.floor(diffDays / 30);
    const diffYears = Math.floor(diffDays / 365);

    if (diffSeconds < 60) return '刚刚';
    if (diffMinutes < 60) return `${diffMinutes}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;
    if (diffWeeks < 4) return `${diffWeeks}周前`;
    if (diffMonths < 12) return `${diffMonths}个月前`;
    return `${diffYears}年前`;
  };

  // 处理隐藏帖子
  const handleHidePost = async (postId, isHidden) => {
    try {
      console.log('🔄 开始隐藏/显示帖子:', { postId, isHidden });

      // 调用API
      console.log('📡 准备调用API:', { postId, isHidden });
      const result = await postApi.toggleHidden(postId, isHidden);
      console.log('✅ API调用成功，返回结果:', result);

      // 更新本地状态
      setPosts(prevPosts => {
        const updatedPosts = prevPosts.map(post =>
          post.id === postId
            ? { ...post, isHidden: isHidden }
            : post
        );

        console.log('📊 帖子状态更新后:', {
          总帖子数: updatedPosts.length,
          隐藏帖子数: updatedPosts.filter(p => p.isHidden).length,
          显示帖子数: updatedPosts.filter(p => !p.isHidden).length,
          更新的帖子: updatedPosts.find(p => p.id === postId)
        });

        return updatedPosts;
      });

      // 显示成功提示
      console.log(`✅ 帖子已${isHidden ? '隐藏' : '显示'}`);

    } catch (error) {
      console.error('❌ 隐藏帖子操作失败:', error);
      throw error; // 让PostCard组件处理错误
    }
  };

  useEffect(() => {
    const tag = params.filterTag || params.tab;
    if (tag && tabs.includes(tag)) {
      setFilterTag(tag);
      fetchPosts(tag);
    } else {
      setFilterTag(null);
      fetchPosts();
    }
  }, [params.filterTag, params.tab]);

  const onRefresh = () => {
    setRefreshing(true);
    fetchPosts(filterTag).finally(() => setRefreshing(false));
  };

  return (
    <ThemedView style={styles.container}>
      {/* 顶部标题栏（包含安全区域） */}
      <View style={styles.fixedHeader}>
        <TouchableOpacity style={styles.headerIcon} onPress={() => setSidebarVisible(true)}>
          <Image source={require('../../assets/FrameThree.png')} style={styles.iconImage} />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>社区</ThemedText>
        <TouchableOpacity style={styles.headerIcon} onPress={() => router.push('../Chat')}>
          <Image source={require('../../assets/FrameFour.png')} style={styles.iconImage} />
        </TouchableOpacity>
      </View>

      {/* 侧边栏 */}
      <ProfileSidebar visible={sidebarVisible} onClose={() => setSidebarVisible(false)} />

      {/* 帖子内容区 */}
      <ScrollView
        style={styles.postsContainer}
        contentContainerStyle={{ paddingTop: 130, paddingBottom: 120 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={['#4A90E2']} />
        }
      >
        {/* 四个卡片Tab */}
        <View style={styles.tabRow}>
          {tabs.map((tab, index) => {
            const colors = ['#F1E2FF', '#FFDDB4', '#E2ECE1', '#DCECF9'];
            const selected = tab === filterTag;
            return (
              <TouchableOpacity
                key={tab}
                style={[
                  styles.tabCardBox,
                  selected && styles.tabCardBoxActive,
                  (tab === '学习搭子' || tab === '职通车') && styles.tabCardBoxHigher
                ]}
                onPress={() => router.push(tabToPage[tab])}
              >
                <LinearGradient
                  colors={[colors[index], colors[index]]}
                  style={styles.tabCardContent}
                >
                  <ThemedText style={[styles.tabCardText, selected && { fontWeight: 'bold' }]}>
                    {tab}
                  </ThemedText>
                </LinearGradient>
              </TouchableOpacity>
            );
          })}
        </View>

        {/* 筛选指示器 */}
        {filterTag && (
          <View style={styles.filterIndicator}>
            <ThemedText style={styles.filterText}>当前显示: {filterTag}</ThemedText>
            <TouchableOpacity onPress={() => {
              setFilterTag(null);
              fetchPosts();
            }}>
              <ThemedText style={styles.clearFilterText}>显示全部</ThemedText>
            </TouchableOpacity>
          </View>
        )}

        {/* 加载中状态 */}
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#4A90E2" />
            <ThemedText style={styles.loadingText}>加载中...</ThemedText>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <ThemedText style={styles.errorText}>{error}</ThemedText>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => fetchPosts(filterTag)}
            >
              <ThemedText style={styles.retryButtonText}>重试</ThemedText>
            </TouchableOpacity>
          </View>
        ) : (
          (() => {
            const visiblePosts = posts.filter(post => !post.isHidden);
            console.log('📋 帖子显示过滤:', {
              总帖子数: posts.length,
              隐藏帖子数: posts.filter(p => p.isHidden).length,
              显示帖子数: visiblePosts.length,
              隐藏的帖子IDs: posts.filter(p => p.isHidden).map(p => p.id)
            });
            return visiblePosts;
          })()
            .map(post => (
              <PostCard
                key={post.id}
                post={post}
                onPostPress={() => {
                  console.log('🔄 点击帖子，准备导航:', { postId: post.id, postTitle: post.title });
                  router.push(`/Community/Post?id=${post.id}`);
                }}
                onLikeToggle={async (postId, isLiked) => {
                  try {
                    // 乐观更新UI
                    setPosts(prev => prev.map(p =>
                      p.id === postId
                        ? {
                          ...p,
                          likeCount: isLiked ? p.likeCount + 1 : p.likeCount - 1,
                          likes: isLiked ? (p.likes || 0) + 1 : (p.likes || 0) - 1
                        }
                        : p
                    ));

                    // 调用API
                    if (isLiked) {
                      await postApi.like(postId);
                    } else {
                      await postApi.unlike(postId);
                    }
                  } catch (error) {
                    console.error('点赞操作失败:', error);
                    // 回滚UI更改
                    setPosts(prev => prev.map(p =>
                      p.id === postId
                        ? {
                          ...p,
                          likeCount: isLiked ? p.likeCount - 1 : p.likeCount + 1,
                          likes: isLiked ? (p.likes || 0) - 1 : (p.likes || 0) + 1
                        }
                        : p
                    ));
                  }
                }}
                onCommentPress={wrapGuestAction(() => console.log('评论', post.id), 'community-interaction')}
                onForwardPress={wrapGuestAction(() => console.log('转发', post.id), 'community-interaction')}
                onHidePost={wrapGuestAction(handleHidePost, 'community-interaction')}
              />
            ))
        )}
      </ScrollView>

      {/* 游客权限弹窗 */}
      <GuestPermissionModal
        visible={showPermissionModal}
        onClose={closePermissionModal}
        onRegister={handleRegister}
        featureName={currentFeature.name}
        description={currentFeature.description}
      />
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  fixedHeader: {
    position: 'absolute',
    top: 0, // 从顶部开始，包含安全区域
    left: 0,
    right: 0,
    height: 108, // 44(安全区域) + 64(标题栏) = 108
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end', // 内容对齐到底部
    paddingHorizontal: 16,
    paddingBottom: 12, // 底部内边距
    backgroundColor: 'rgba(255, 248, 243, 0.95)', // 半透明效果
    backdropFilter: 'blur(10px)', // 毛玻璃效果（iOS支持）
    borderBottomWidth: 0.5,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
    zIndex: 10
  },
  headerTitle: { fontSize: 18, fontWeight: 'bold', color: '#000' },
  headerIcon: { padding: 8 },
  iconImage: { width: 24, height: 24, resizeMode: 'contain' },
  postsContainer: { flex: 1 },
  tabRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginHorizontal: 12,
    marginTop: 16,
    marginBottom: 10
  },
  tabCardBox: {
    width: 75,
    height: 90,
    borderRadius: 16,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tabCardBoxHigher: {
    marginTop: -16
  },
  tabCardBoxActive: {
    borderWidth: 2,
    borderColor: '#4A90E2'
  },
  tabCardContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%'
  },
  tabCardText: {
    fontSize: 13,
    color: '#333',
    fontWeight: '600',
    textAlign: 'center'
  },
  filterIndicator: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: 16,
    padding: 12,
    backgroundColor: '#eee',
    borderRadius: 10
  },
  filterText: { fontSize: 14, color: '#555' },
  clearFilterText: { fontSize: 14, color: '#007AFF' },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#ff4444',
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#4A90E2',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default Community;
