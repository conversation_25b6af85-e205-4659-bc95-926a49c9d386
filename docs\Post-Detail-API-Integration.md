# 帖子详情页API集成重构

## 🎯 重构目标

将帖子详情页面 `Post.jsx` 从硬编码的模拟数据改为调用真实的API，与社区页面的PostCard组件使用相同的API和数据处理逻辑。

## 🔄 主要改进

### 1. API服务导入更新 ✅

**修改前**:
```javascript
import apiServices from '../../lib/apiServices';
```

**修改后**:
```javascript
import { postApi, authApi } from '../../lib/apiServices';
```

### 2. 状态管理增强 ✅

**新增状态**:
```javascript
const [loading, setLoading] = useState(true);
const [error, setError] = useState(null);
const [currentTime, setCurrentTime] = useState(new Date());
```

**实时时间更新**:
```javascript
useEffect(() => {
  const timer = setInterval(() => {
    setCurrentTime(new Date());
  }, 60000); // 每分钟更新一次

  return () => clearInterval(timer);
}, []);
```

### 3. 时间格式化函数 ✅

**与PostCard一致的时间处理**:
```javascript
const formatTimeFromNow = (createTime) => {
  if (!createTime) return '未知时间';
  
  const createDate = new Date(createTime);
  const now = currentTime;
  const diffMs = now.getTime() - createDate.getTime();
  
  // 多级时间精度显示
  if (diffSeconds < 60) return '刚刚';
  if (diffMinutes < 60) return `${diffMinutes}分钟前`;
  if (diffHours < 24) return `${diffHours}小时前`;
  if (diffDays < 7) return `${diffDays}天前`;
  if (diffWeeks < 4) return `${diffWeeks}周前`;
  if (diffMonths < 12) return `${diffMonths}个月前`;
  return `${diffYears}年前`;
};
```

### 4. 真实数据获取 ✅

**帖子详情获取**:
```javascript
// 1. 获取帖子详情
const postResponse = await postApi.getById(params.id);
const postData = postResponse.data;

// 2. 获取帖子作者信息
let userInfo = null;
if (postData.userId) {
  try {
    userInfo = await authApi.getUserById(postData.userId);
  } catch (userError) {
    console.warn('获取用户信息失败:', userError);
  }
}

// 3. 格式化帖子数据
const formattedPost = {
  id: postData.id,
  title: postData.title,
  content: postData.content,
  imageUrls: postData.imageUrls ? postData.imageUrls.split(',').filter(Boolean) : [],
  tag: postData.tag,
  likeCount: postData.likeCount || 0,
  commentCount: postData.commentCount || 0,
  viewCount: postData.viewCount || 0,
  createTime: postData.createTime,
  userId: postData.userId,
  user: userInfo?.userAccount || userInfo?.userName || postData.userId || 'Unknown',
  userAvatar: userInfo?.userAvatar || null,
  userInfo: userInfo || null,
  hasMedia: postData.imageUrls && postData.imageUrls.trim().length > 0,
};
```

**评论列表获取**:
```javascript
// 4. 获取评论列表
const commentsResponse = await postApi.getComment(params.id);
const commentsData = commentsResponse.data || [];

// 5. 批量获取评论作者信息
const commentUserIds = [...new Set(commentsData.map(comment => comment.userId).filter(Boolean))];
let commentUsersMap = {};

if (commentUserIds.length > 0) {
  try {
    const commentUsers = await authApi.getUsersByIds(commentUserIds);
    commentUsersMap = commentUsers.reduce((map, user) => {
      map[user.id] = user;
      return map;
    }, {});
  } catch (userError) {
    console.warn('获取评论用户信息失败:', userError);
  }
}

// 6. 格式化评论数据
const formattedComments = commentsData.map(comment => ({
  id: comment.id,
  content: comment.content,
  createTime: comment.createTime,
  likeCount: comment.likeCount || 0,
  userId: comment.userId,
  user: commentUsersMap[comment.userId]?.userAccount || commentUsersMap[comment.userId]?.userName || comment.userId || 'Unknown',
  userAvatar: commentUsersMap[comment.userId]?.userAvatar || null,
  userInfo: commentUsersMap[comment.userId] || null,
  isLiked: false, // TODO: 需要从API获取当前用户的点赞状态
}));
```

### 5. 动态用户头像系统 ✅

**与PostCard一致的头像处理**:
```javascript
const getUserAvatar = (userObj) => {
  // 支持传入用户对象或用户名字符串
  const user = typeof userObj === 'string' ? userObj : userObj?.user;
  const userAvatar = typeof userObj === 'object' ? userObj?.userAvatar : null;
  
  // 优先使用用户的真实头像
  if (userAvatar) {
    return (
      <Image 
        source={{ uri: userAvatar }} 
        style={styles.avatar}
        defaultSource={avatarJessica}
      />
    );
  }
  
  // 预设头像或生成字母头像
  switch (user) {
    case 'Jessica':
    case 'Kin':
    case 'Caaary':
      return <Image source={avatarMap[user]} style={styles.avatar} />;
    default:
      // 根据用户ID生成颜色和字母头像
      const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
      const colorIndex = (user || '').length % colors.length;
      return (
        <View style={[styles.avatar, { backgroundColor: colors[colorIndex], justifyContent: 'center', alignItems: 'center' }]}>
          <ThemedText style={{ color: '#fff', fontSize: 16, fontWeight: 'bold' }}>
            {(user || 'U').charAt(0).toUpperCase()}
          </ThemedText>
        </View>
      );
  }
};
```

### 6. 真实的评论发布功能 ✅

**API调用评论发布**:
```javascript
const handleSubmitComment = wrapGuestAction(async () => {
  if (!newComment.trim()) {
    Alert.alert('提示', '请输入评论内容');
    return;
  }

  setIsSubmitting(true);
  try {
    console.log('🔄 开始发布评论:', { postId: post.id, content: newComment.trim() });

    // 调用API发布评论
    const result = await postApi.addComment(post.id, newComment.trim());
    console.log('✅ 评论发布成功:', result);

    // 获取当前用户信息
    let currentUser = null;
    try {
      currentUser = await authApi.getCurrentUser();
    } catch (userError) {
      console.warn('获取当前用户信息失败:', userError);
    }

    // 创建新评论对象
    const newCommentObj = {
      id: result.data?.id || Date.now().toString(),
      content: newComment.trim(),
      createTime: new Date().toISOString(),
      likeCount: 0,
      userId: currentUser?.id || 'current_user',
      user: currentUser?.userAccount || currentUser?.userName || '我',
      userAvatar: currentUser?.userAvatar || null,
      userInfo: currentUser || null,
      isLiked: false,
    };

    // 添加到评论列表顶部
    setComments(prev => [newCommentObj, ...prev]);
    
    // 更新帖子的评论数
    setPost(prev => prev ? {
      ...prev,
      commentCount: (prev.commentCount || 0) + 1
    } : prev);

    setNewComment('');
    Alert.alert('成功', '评论发布成功！');

  } catch (error) {
    console.error('❌ 发布评论失败:', error);
    Alert.alert('错误', `发布评论失败: ${error.message}`);
  } finally {
    setIsSubmitting(false);
  }
}, 'community-interaction');
```

### 7. 真实的点赞功能 ✅

**API调用点赞/取消点赞**:
```javascript
<AnimatedLikeButton
  isLiked={false} // TODO: 需要从API获取当前用户的点赞状态
  likeCount={post.likeCount}
  onLikeToggle={async (liked) => {
    try {
      console.log('🔄 帖子点赞操作:', { postId: post.id, liked });
      
      if (liked) {
        await postApi.like(post.id);
        console.log('✅ 点赞成功');
        // 更新帖子点赞数
        setPost(prev => prev ? {
          ...prev,
          likeCount: (prev.likeCount || 0) + 1
        } : prev);
      } else {
        await postApi.unlike(post.id);
        console.log('✅ 取消点赞成功');
        // 更新帖子点赞数
        setPost(prev => prev ? {
          ...prev,
          likeCount: Math.max((prev.likeCount || 0) - 1, 0)
        } : prev);
      }
    } catch (error) {
      console.error('❌ 点赞操作失败:', error);
      throw error; // 让AnimatedLikeButton处理错误回滚
    }
  }}
/>
```

### 8. 动态媒体内容显示 ✅

**支持多张图片显示**:
```javascript
{/* 媒体内容 */}
{post.hasMedia && post.imageUrls && post.imageUrls.length > 0 && (
  <View style={styles.mediaContainer}>
    {post.imageUrls.map((imageUrl, index) => (
      <Image
        key={index}
        source={{ uri: imageUrl }}
        style={[styles.postImage, { marginBottom: index < post.imageUrls.length - 1 ? 8 : 0 }]}
        defaultSource={require('../../assets/Community_image/PhotosOne.png')}
      />
    ))}
  </View>
)}
```

## 🔧 技术改进

### 数据流程
```
URL参数 → API调用 → 用户信息获取 → 数据格式化 → UI渲染
```

### 错误处理
- ✅ **加载状态管理** - loading/error状态
- ✅ **API错误处理** - try/catch包装
- ✅ **用户友好提示** - Alert显示错误信息
- ✅ **降级处理** - 用户信息获取失败时的备选方案

### 性能优化
- ✅ **批量用户信息获取** - 减少API调用次数
- ✅ **实时时间更新** - 定时器管理
- ✅ **内存管理** - useEffect清理函数

## 🚀 部署建议

### 测试验证
1. **帖子详情加载** - 验证帖子信息正确显示
2. **用户信息显示** - 确认用户名和头像正确
3. **时间显示** - 验证时间格式化和实时更新
4. **评论功能** - 测试评论发布和显示
5. **点赞功能** - 验证点赞/取消点赞操作
6. **媒体内容** - 确认图片正确加载

### 待完善功能
- [ ] 获取当前用户的点赞状态
- [ ] 评论点赞功能API集成
- [ ] 图片查看器功能
- [ ] 分享功能实现

---

**重构完成时间**: 2024-08-02  
**影响范围**: 帖子详情页面完整功能  
**技术改进**: 真实API集成 + 动态数据显示 + 统一用户体验
