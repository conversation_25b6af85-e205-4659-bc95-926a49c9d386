// lib/imageCacheManager.js - 图片缓存管理器，防止重复加载
import { Platform } from 'react-native';

class ImageCacheManager {
  constructor() {
    this.cache = new Map();
    this.loadingPromises = new Map();
    this.maxCacheSize = 100; // 最大缓存数量
    this.cacheHitCount = 0;
    this.cacheMissCount = 0;
  }

  /**
   * 生成缓存键
   * @param {string} url - 图片URL
   * @returns {string} - 缓存键
   */
  generateCacheKey(url) {
    if (!url) return '';
    return url.replace(/[^a-zA-Z0-9]/g, '_');
  }

  /**
   * 检查图片是否已缓存
   * @param {string} url - 图片URL
   * @returns {boolean} - 是否已缓存
   */
  isCached(url) {
    const key = this.generateCacheKey(url);
    return this.cache.has(key);
  }

  /**
   * 获取缓存的图片状态
   * @param {string} url - 图片URL
   * @returns {Object|null} - 缓存的图片状态
   */
  getCachedImageStatus(url) {
    const key = this.generateCacheKey(url);
    if (this.cache.has(key)) {
      this.cacheHitCount++;
      const cached = this.cache.get(key);
      // 更新访问时间
      cached.lastAccessed = Date.now();
      return cached;
    }
    this.cacheMissCount++;
    return null;
  }

  /**
   * 缓存图片状态
   * @param {string} url - 图片URL
   * @param {Object} status - 图片状态 {loaded: boolean, error: boolean, timestamp: number}
   */
  setCachedImageStatus(url, status) {
    const key = this.generateCacheKey(url);
    
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxCacheSize) {
      this.evictOldestEntry();
    }

    this.cache.set(key, {
      ...status,
      url,
      timestamp: Date.now(),
      lastAccessed: Date.now()
    });

    console.log(`[ImageCache] 缓存图片状态: ${url} -> ${status.loaded ? '成功' : '失败'}`);
  }

  /**
   * 删除最旧的缓存条目
   */
  evictOldestEntry() {
    let oldestKey = null;
    let oldestTime = Date.now();

    for (const [key, value] of this.cache.entries()) {
      if (value.lastAccessed < oldestTime) {
        oldestTime = value.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      console.log(`[ImageCache] 删除最旧缓存条目: ${oldestKey}`);
    }
  }

  /**
   * 预加载图片
   * @param {string} url - 图片URL
   * @returns {Promise<boolean>} - 是否加载成功
   */
  async preloadImage(url) {
    if (!url) return false;

    const key = this.generateCacheKey(url);
    
    // 检查是否已经在加载中
    if (this.loadingPromises.has(key)) {
      return this.loadingPromises.get(key);
    }

    // 检查缓存
    const cached = this.getCachedImageStatus(url);
    if (cached) {
      return cached.loaded;
    }

    // 创建加载Promise
    const loadingPromise = this.loadImageInternal(url);
    this.loadingPromises.set(key, loadingPromise);

    try {
      const result = await loadingPromise;
      this.setCachedImageStatus(url, { loaded: result, error: !result });
      return result;
    } finally {
      this.loadingPromises.delete(key);
    }
  }

  /**
   * 内部图片加载方法
   * @param {string} url - 图片URL
   * @returns {Promise<boolean>} - 是否加载成功
   */
  loadImageInternal(url) {
    return new Promise((resolve) => {
      if (Platform.OS === 'web') {
        const img = new Image();
        img.onload = () => resolve(true);
        img.onerror = () => resolve(false);
        img.src = url;
      } else {
        // React Native环境
        const { Image } = require('react-native');
        Image.prefetch(url)
          .then(() => resolve(true))
          .catch(() => resolve(false));
      }
    });
  }

  /**
   * 批量预加载图片
   * @param {string[]} urls - 图片URL数组
   * @returns {Promise<boolean[]>} - 加载结果数组
   */
  async preloadImages(urls) {
    if (!Array.isArray(urls)) return [];
    
    const promises = urls.map(url => this.preloadImage(url));
    return Promise.all(promises);
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache.clear();
    this.loadingPromises.clear();
    this.cacheHitCount = 0;
    this.cacheMissCount = 0;
    console.log('[ImageCache] 缓存已清除');
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} - 缓存统计
   */
  getCacheStats() {
    return {
      cacheSize: this.cache.size,
      maxCacheSize: this.maxCacheSize,
      cacheHitCount: this.cacheHitCount,
      cacheMissCount: this.cacheMissCount,
      hitRate: this.cacheHitCount / (this.cacheHitCount + this.cacheMissCount) || 0,
      loadingCount: this.loadingPromises.size
    };
  }

  /**
   * 打印缓存统计信息
   */
  printCacheStats() {
    const stats = this.getCacheStats();
    console.log('[ImageCache] 缓存统计:', {
      缓存大小: `${stats.cacheSize}/${stats.maxCacheSize}`,
      命中次数: stats.cacheHitCount,
      未命中次数: stats.cacheMissCount,
      命中率: `${(stats.hitRate * 100).toFixed(1)}%`,
      正在加载: stats.loadingCount
    });
  }

  /**
   * 移除特定URL的缓存
   * @param {string} url - 图片URL
   */
  removeCachedImage(url) {
    const key = this.generateCacheKey(url);
    this.cache.delete(key);
    this.loadingPromises.delete(key);
  }

  /**
   * 检查图片是否正在加载
   * @param {string} url - 图片URL
   * @returns {boolean} - 是否正在加载
   */
  isLoading(url) {
    const key = this.generateCacheKey(url);
    return this.loadingPromises.has(key);
  }
}

// 创建全局单例
const imageCacheManager = new ImageCacheManager();

export default imageCacheManager;

// 导出常用方法
export const {
  preloadImage,
  preloadImages,
  isCached,
  getCachedImageStatus,
  clearCache,
  getCacheStats,
  printCacheStats,
  isLoading
} = imageCacheManager;
