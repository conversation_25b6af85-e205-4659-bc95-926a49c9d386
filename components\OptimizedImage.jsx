// components/OptimizedImage.jsx - 优化的图片组件，避免重复URL处理
import React, { useState, useMemo, useCallback } from 'react';
import { Image, View, Platform } from 'react-native';
import { transformImageUrl } from '../lib/imageUtils';
import ThemedText from './ThemedText';

const OptimizedImage = ({ 
  source, 
  style, 
  placeholder = 'https://via.placeholder.com/300x200?text=No+Image',
  showDebugInfo = false,
  onError,
  onLoad,
  ...props 
}) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 使用useMemo缓存URL处理结果，只在source变化时重新计算
  const processedImageSource = useMemo(() => {
    if (!source) {
      return { uri: placeholder };
    }

    let originalUrl = '';
    if (typeof source === 'string') {
      originalUrl = source;
    } else if (source?.uri) {
      originalUrl = source.uri;
    } else {
      return { uri: placeholder };
    }

    try {
      const finalUrl = transformImageUrl(originalUrl);
      
      if (typeof source === 'object' && source.uri) {
        return { ...source, uri: finalUrl };
      }
      
      return { uri: finalUrl };
    } catch (error) {
      console.error(`[${Platform.OS}] 图片URL处理失败:`, error);
      return { uri: placeholder };
    }
  }, [source, placeholder]);

  // 使用useCallback避免函数重复创建
  const handleError = useCallback((error) => {
    console.error(`[${Platform.OS}] 图片加载失败:`, {
      source: processedImageSource,
      error: error.nativeEvent?.error || error
    });
    
    setImageError(true);
    setIsLoading(false);
    
    if (onError) {
      onError(error);
    }
  }, [processedImageSource, onError]);

  const handleLoad = useCallback((event) => {
    setIsLoading(false);
    setImageError(false);
    
    if (onLoad) {
      onLoad(event);
    }
  }, [onLoad]);

  const handleLoadStart = useCallback(() => {
    setIsLoading(true);
    setImageError(false);
  }, []);

  // 如果图片加载失败，显示占位符
  if (imageError) {
    return (
      <View style={[style, { justifyContent: 'center', alignItems: 'center', backgroundColor: '#f0f0f0' }]}>
        <ThemedText style={{ fontSize: 12, color: '#666' }}>图片加载失败</ThemedText>
        {showDebugInfo && (
          <ThemedText style={{ fontSize: 10, color: '#999', marginTop: 5, textAlign: 'center' }}>
            URL: {processedImageSource.uri?.substring(0, 50)}...
          </ThemedText>
        )}
      </View>
    );
  }

  return (
    <View style={style}>
      <Image
        source={processedImageSource}
        style={[{ width: '100%', height: '100%' }, style]}
        onError={handleError}
        onLoad={handleLoad}
        onLoadStart={handleLoadStart}
        {...props}
      />
      
      {/* 调试信息 */}
      {showDebugInfo && !imageError && (
        <View style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: 'rgba(0,0,0,0.7)',
          padding: 5
        }}>
          <ThemedText style={{ fontSize: 9, color: 'white' }}>
            {typeof source === 'string' ? source : source?.uri || 'Unknown'}
          </ThemedText>
          <ThemedText style={{ fontSize: 8, color: '#ccc' }}>
            → {processedImageSource.uri?.substring(0, 40)}...
          </ThemedText>
        </View>
      )}
      
      {/* 加载指示器 */}
      {isLoading && (
        <View style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(255,255,255,0.8)'
        }}>
          <ThemedText style={{ fontSize: 12, color: '#666' }}>加载中...</ThemedText>
        </View>
      )}
    </View>
  );
};

export default OptimizedImage;
