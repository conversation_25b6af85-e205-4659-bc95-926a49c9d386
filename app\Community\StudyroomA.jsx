import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, TouchableOpacity, Image, PanResponder, Animated, Alert, Dimensions } from 'react-native';
import { useRouter } from 'expo-router';
import ThemedView from '../../components/ThemedView';
import ThemedText from '../../components/ThemedText';


const StudyroomA = () => {
  const router = useRouter();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [studyTime, setStudyTime] = useState(0); // 学习时间（秒）

  // 悬浮按钮状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragType, setDragType] = useState(null); // 'coffee' 或 'fireworks'
  const [effects, setEffects] = useState([]); // 特效数组
  const [userPositions, setUserPositions] = useState({}); // 用户头像位置

  // 动画值
  const coffeePosition = useRef(new Animated.ValueXY()).current;
  const fireworksPosition = useRef(new Animated.ValueXY()).current;
  const dragPosition = useRef(new Animated.ValueXY()).current;

  // 手势处理器
  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      return Math.abs(gestureState.dx) > Math.abs(gestureState.dy) && Math.abs(gestureState.dx) > 10;
    },
    onPanResponderMove: (evt, gestureState) => {
      // 可以在这里添加拖拽动画效果
    },
    onPanResponderRelease: (evt, gestureState) => {
      // 左滑超过50px就收起菜单
      if (gestureState.dx < -50) {
        setIsMenuOpen(false);
      }
    },
  });

  // 计时器效果
  useEffect(() => {
    const timer = setInterval(() => {
      setStudyTime(prev => prev + 1);
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // 格式化时间
  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours}h ${minutes}min ${secs}s`;
  };

  // 创建拖拽手势处理器
  const createDragPanResponder = (type) => {
    let longPressTimer = null;
    let hasStartedDrag = false;

    return PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        // 只有在长按后才开始拖拽
        return hasStartedDrag && (Math.abs(gestureState.dx) > 5 || Math.abs(gestureState.dy) > 5);
      },
      onPanResponderGrant: (evt) => {
        hasStartedDrag = false;

        // 设置长按定时器
        longPressTimer = setTimeout(() => {
          hasStartedDrag = true;
          setIsDragging(true);
          setDragType(type);

          // 设置初始拖拽位置为按钮位置
          const initialX = evt.nativeEvent.pageX - 30; // 减去图标宽度的一半
          const initialY = evt.nativeEvent.pageY - 30;
          dragPosition.setValue({ x: initialX, y: initialY });

          // 触觉反馈（如果支持）
          if (typeof navigator !== 'undefined' && navigator.vibrate) {
            navigator.vibrate(50);
          }
        }, 500); // 500ms长按
      },
      onPanResponderMove: (evt, gestureState) => {
        if (hasStartedDrag) {
          // 更新拖拽位置为当前触摸位置
          dragPosition.setValue({
            x: evt.nativeEvent.pageX - 30,
            y: evt.nativeEvent.pageY - 30
          });
        }
      },
      onPanResponderRelease: (evt, gestureState) => {
        // 清除长按定时器
        if (longPressTimer) {
          clearTimeout(longPressTimer);
          longPressTimer = null;
        }

        if (hasStartedDrag) {
          setIsDragging(false);

          // 检查是否拖拽到用户头像上
          const dropX = evt.nativeEvent.pageX;
          const dropY = evt.nativeEvent.pageY;

          const success = checkCollisionWithUsers(dropX, dropY, type);

          // 重置拖拽位置
          Animated.spring(dragPosition, {
            toValue: { x: 0, y: 0 },
            useNativeDriver: false,
          }).start();

          setDragType(null);
          hasStartedDrag = false;
        }
      },
      onPanResponderTerminate: () => {
        // 处理手势被中断的情况
        if (longPressTimer) {
          clearTimeout(longPressTimer);
          longPressTimer = null;
        }
        setIsDragging(false);
        setDragType(null);
        hasStartedDrag = false;

        Animated.spring(dragPosition, {
          toValue: { x: 0, y: 0 },
          useNativeDriver: false,
        }).start();
      },
    });
  };

  // 检查碰撞并触发特效
  const checkCollisionWithUsers = (x, y, type) => {
    // 获取屏幕尺寸
    const screenWidth = Dimensions.get('window').width;
    const screenHeight = Dimensions.get('window').height;

    // 计算右侧卡片区域的范围
    const cardAreaLeft = screenWidth * 0.2; // 左侧菜单占20%
    const cardAreaRight = screenWidth - 16; // 右边距16px
    const cardAreaTop = 120; // 头部高度
    const cardAreaBottom = screenHeight - 200; // 底部按钮和计时器区域

    // 检查是否在卡片区域内
    if (x >= cardAreaLeft && x <= cardAreaRight && y >= cardAreaTop && y <= cardAreaBottom) {
      // 计算每个卡片的位置（2x2网格）
      const cardWidth = (cardAreaRight - cardAreaLeft) / 2;
      const cardHeight = (cardAreaBottom - cardAreaTop) / 2;

      // 确定拖拽到了哪个卡片
      const cardCol = Math.floor((x - cardAreaLeft) / cardWidth);
      const cardRow = Math.floor((y - cardAreaTop) / cardHeight);
      const cardIndex = cardRow * 2 + cardCol;

      // 检查卡片索引是否有效
      if (cardIndex >= 0 && cardIndex < onlineUsers.length) {
        const targetUser = onlineUsers[cardIndex];
        triggerEffect(type, targetUser, x, y);

        // 显示成功提示
        const actionText = type === 'coffee' ? '送了一杯咖啡' : '送了一束鲜花';
        Alert.alert('成功！', `你给 ${targetUser.name} ${actionText}！`);
        return true;
      }
    }

    // 如果没有拖拽到有效位置，显示提示
    Alert.alert('提示', '请将图标拖拽到队友头像上哦！');
    return false;
  };

  // 触发特效
  const triggerEffect = (type, targetUser, x, y) => {
    const effectId = Date.now();
    const newEffect = {
      id: effectId,
      type: type,
      targetUser: targetUser,
      x: x,
      y: y,
      opacity: new Animated.Value(1),
      scale: new Animated.Value(0.3),
      translateY: new Animated.Value(0),
    };

    setEffects(prev => [...prev, newEffect]);

    // 特效动画序列
    Animated.sequence([
      // 第一阶段：快速放大并上升
      Animated.parallel([
        Animated.spring(newEffect.scale, {
          toValue: 1.2,
          tension: 100,
          friction: 8,
          useNativeDriver: false,
        }),
        Animated.timing(newEffect.translateY, {
          toValue: -30,
          duration: 500,
          useNativeDriver: false,
        }),
      ]),
      // 第二阶段：继续上升并淡出
      Animated.parallel([
        Animated.timing(newEffect.opacity, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(newEffect.translateY, {
          toValue: -80,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(newEffect.scale, {
          toValue: 0.8,
          duration: 1000,
          useNativeDriver: false,
        }),
      ]),
    ]).start(() => {
      // 动画完成后移除特效
      setEffects(prev => prev.filter(effect => effect.id !== effectId));
    });
  };

  // 处理按钮点击
  const handleButtonPress = (type) => {
    const message = type === 'coffee'
      ? '送队友一杯咖啡吧!长按图标拖拽到队友身上'
      : '送队友一束鲜花吧!长按图标拖拽到队友身上';

    Alert.alert('提示', message);
  };

  // Sample data for online users
  const onlineUsers = [
    { id: '1', name: 'Jessica', avatar: require('../../assets/Community_image/AOne.png') },
    { id: '2', name: 'Cary', avatar: require('../../assets/Community_image/ATwo.png') },
    { id: '3', name: 'Lucy', avatar: require('../../assets/Community_image/AThree.png') },
    { id: '4', name: 'Lucy', avatar: require('../../assets/Community_image/AFour.png') },
    { id: '5', name: 'Alex', avatar: require('../../assets/Community_image/AFive.png') },
  ];

  return (
    <ThemedView style={{ flex: 1, backgroundColor: '#FFF8F3' }}>
      {/* 安全视图 */}
      <View style={styles.safeArea} />

      {/* 顶部标题栏 */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Image source={require('../../assets/FrameTwo.png')} style={styles.backIcon} />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>自习室名称</ThemedText>

      </View>

      {/* 主要内容 */}
      <View style={styles.mainContent}>
        {/* 左侧侧菜单栏 */}
        <View style={styles.leftSidebar}>
          <TouchableOpacity
            style={styles.menuButton}
            onPress={() => setIsMenuOpen(!isMenuOpen)}
          >
            <View style={styles.verticalTextContainer}>
              <ThemedText style={styles.verticalText}>在</ThemedText>
              <ThemedText style={styles.verticalText}>线</ThemedText>
              <ThemedText style={styles.verticalText}>列</ThemedText>
              <ThemedText style={styles.verticalText}>表</ThemedText>
            </View>
          </TouchableOpacity>

          {/* 弹出菜单 */}
          {isMenuOpen && (
            <View style={styles.dropdownMenu} {...panResponder.panHandlers}>
              <ThemedText style={styles.menuTitle}>在线列表</ThemedText>
              {onlineUsers.map((user) => (
                <View key={user.id} style={styles.userItem}>
                  <Image source={user.avatar} style={styles.userAvatar} />
                  <ThemedText style={styles.userName}>{user.name}</ThemedText>
                </View>
              ))}
              {/* 底部收起箭头 */}
              <TouchableOpacity
                style={styles.collapseButton}
                onPress={() => setIsMenuOpen(false)}
              >
                <ThemedText style={styles.arrowText}>← 左滑收起</ThemedText>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* 右侧卡片区域 */}
        <View style={styles.rightCards}>
          {onlineUsers.slice(0, 4).map((user, index) => (
            <View key={user.id} style={styles.studyCard}>
              <View style={styles.cardTop}>
                <View style={styles.cardUserInfo}>
                  <Image source={user.avatar} style={styles.cardAvatar} />
                  <ThemedText style={styles.cardUserName}>{user.name}</ThemedText>
                </View>
              </View>
              <View style={styles.cardMiddle} />
              <View style={styles.cardBottom}>
                <Image
                  source={require('../../assets/Community_image/Studying.png')}
                  style={styles.studyingIcon}
                />
              </View>
            </View>
          ))}
        </View>
      </View>

      {/* 悬浮按钮区域 */}
      <View style={styles.floatingButtonsContainer}>
        {/* 咖啡按钮 */}
        <TouchableOpacity
          style={styles.floatingButton}
          onPress={() => handleButtonPress('coffee')}
          onLongPress={() => { }}
          {...createDragPanResponder('coffee').panHandlers}
        >
          <Image
            source={require('../../assets/Community_image/Coffee.png')}
            style={styles.floatingButtonIcon}
          />
        </TouchableOpacity>

        {/* 烟花按钮 */}
        <TouchableOpacity
          style={styles.floatingButton}
          onPress={() => handleButtonPress('fireworks')}
          onLongPress={() => { }}
          {...createDragPanResponder('fireworks').panHandlers}
        >
          <Image
            source={require('../../assets/Community_image/Fireworks.png')}
            style={styles.floatingButtonIcon}
          />
        </TouchableOpacity>
      </View>

      {/* 拖拽时的图标 */}
      {isDragging && (
        <Animated.View
          style={[
            styles.dragIcon,
            {
              transform: dragPosition.getTranslateTransform(),
            },
          ]}
        >
          <Image
            source={dragType === 'coffee'
              ? require('../../assets/Community_image/Coffee.png')
              : require('../../assets/Community_image/Fireworks.png')
            }
            style={styles.dragIconImage}
          />
        </Animated.View>
      )}

      {/* 特效层 */}
      {effects.map((effect) => (
        <Animated.View
          key={effect.id}
          style={[
            styles.effectContainer,
            {
              left: effect.x - 30, // 居中显示
              top: effect.y - 30,
              opacity: effect.opacity,
              transform: [
                { scale: effect.scale },
                { translateY: effect.translateY }
              ],
            },
          ]}
        >
          <Image
            source={effect.type === 'coffee'
              ? require('../../assets/Community_image/Coffee.png')
              : require('../../assets/Community_image/Fireworks.png')
            }
            style={styles.effectIcon}
          />
          {/* 添加文字提示 */}
          <ThemedText style={styles.effectText}>
            {effect.type === 'coffee' ? '☕' : '🎆'}
          </ThemedText>
        </Animated.View>
      ))}

      {/* 底部计时器 */}
      <View style={styles.timerContainer}>
        <ThemedText style={styles.timerText}>
          当前学习时间: {formatTime(studyTime)}
        </ThemedText>
      </View>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    height: 44,
    backgroundColor: 'transparent',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'transparent',
  },
  backIcon: {
    width: 24,
    height: 24,
    tintColor: '#333',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    textAlign: 'center',
  },
  headerMenu: {
    fontSize: 20,
    color: '#333',
    fontWeight: 'bold',
  },
  mainContent: {
    flex: 1,
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingTop: 10,
  },
  leftSidebar: {
    width: 50,
    paddingRight: 16,
    position: 'relative',
  },
  menuButton: {
    backgroundColor: '#FFE7CD',
    paddingVertical: 15,
    paddingHorizontal: 4,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
    width: 30,
  },
  verticalTextContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  verticalText: {
    fontSize: 12,
    color: '#333',
    fontWeight: 'bold',
    lineHeight: 16,
    textAlign: 'center',
  },
  dropdownMenu: {
    position: 'absolute',
    top: 0,
    left: -20,
    width: 120,
    backgroundColor: '#FFE7CD',
    borderRadius: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 5,
    zIndex: 10,
  },
  menuTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
    textAlign: 'center',
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  userItem: {
    flexDirection: 'column',
    alignItems: 'center',
    marginBottom: 12,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginBottom: 4,
  },
  userName: {
    fontSize: 10,
    color: '#333',
    textAlign: 'center',
  },
  collapseButton: {
    marginTop: 12,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f0f0f0',
    borderRadius: 6,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  arrowText: {
    fontSize: 12,
    color: '#666',
    fontWeight: 'bold',
  },
  cardUserInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardAvatar: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginRight: 6,
  },
  cardUserName: {
    fontSize: 10,
    color: '#333',
    fontWeight: 'bold',
  },
  timerContainer: {
    position: 'absolute',
    bottom: 60,
    left: 0,
    right: 0,
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginHorizontal: 16,
    borderRadius: 8,
  },
  timerText: {
    fontSize: 14,
    color: '#333',
    fontWeight: 'bold',
  },
  rightCards: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    paddingVertical: 20,
  },
  studyCard: {
    width: 250,
    height: 130,
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  cardTop: {
    height: '30%',
    backgroundColor: '#fff',
    padding: 8,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  cardMiddle: {
    height: '50%',
    backgroundColor: '#f5f5f5',
  },
  cardBottom: {
    height: '20%',
    backgroundColor: '#DFC09E',
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    paddingRight: 8,
    paddingTop: 0,
    position: 'relative',
  },
  studyingIcon: {
    width: 80,
    height: 80,
    resizeMode: 'contain',
    position: 'absolute',
    right: 8,
    top: -60,
  },
  // 悬浮按钮样式
  floatingButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 16,
    gap: 30,
  },
  floatingButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
  floatingButtonIcon: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
  },
  // 拖拽图标样式
  dragIcon: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  dragIconImage: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
    opacity: 0.8,
  },
  // 特效样式
  effectContainer: {
    position: 'absolute',
    width: 60,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
    pointerEvents: 'none', // 不阻挡触摸事件
  },
  effectIcon: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
  },
  effectText: {
    fontSize: 20,
    textAlign: 'center',
    marginTop: 2,
  },
  // 计时器样式调整
  timerContainer: {
    backgroundColor: '#FFE7CD',
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  timerText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#8B4513',
  },


});

export default StudyroomA;