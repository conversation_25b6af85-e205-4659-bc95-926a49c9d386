import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, TextInput } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';

const StudyroomMatch = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([]);
  const [showInitialContent, setShowInitialContent] = useState(true);
  const [isFollowing, setIsFollowing] = useState(false);
  const [commonTags, setCommonTags] = useState(['考试冲刺', '拖延症互助', '自律型']);





  useEffect(() => {
    if (params.tags) {
      const tagsFromParams = decodeURIComponent(params.tags).split(',');
      setCommonTags(tagsFromParams);
      console.log('接收到的标签:', tagsFromParams);
    }
  }, [params.tags]);

  const handleSendMessage = () => {
    if (message.trim()) {
      const newMessage = {
        id: Date.now(),
        text: message.trim(),
        sender: 'user',
        timestamp: new Date()
      };
      setMessages([...messages, newMessage]);
      setMessage('');
      setShowInitialContent(false);
    }
  };

  const handleKeyPress = (event) => {
    if (event.nativeEvent.key === 'Enter') {
      event.preventDefault();
      handleSendMessage();
    }
  };

  const handleFollow = () => {
    setIsFollowing(!isFollowing);
  };



  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Image
            source={require('../../assets/FrameTwo.png')}
            style={styles.icon}
          />
        </TouchableOpacity>
        <Text style={styles.title}>我的搭子匹配进入</Text>
        <TouchableOpacity style={styles.settingsButton}>
          <Text style={styles.menuIcon}>⋯</Text>
        </TouchableOpacity>
      </View>

      {/* 用户头像和连接线 */}
      <View style={styles.userConnection}>
        <View style={styles.userAvatarContainer}>
          <Image
            source={require('../../assets/Community_image/AvatarOne.png')}
            style={styles.avatar}
          />
          <Text style={styles.userName}>Jessica</Text>
        </View>

        <View style={styles.connectionLine}>
          <View style={styles.line} />
          <View style={styles.connectionDot} />
          <View style={styles.line} />
        </View>

        <View style={styles.userAvatarContainer}>
          <View style={styles.avatarWrapper}>
            <Image
              source={require('../../assets/Community_image/AvatarTwo.png')}
              style={styles.avatar}
            />
            <TouchableOpacity style={styles.followButton} onPress={handleFollow}>
              <Text style={styles.followIcon}>
                {isFollowing ? '✓' : '+'}
              </Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.userName}>Lucy</Text>
        </View>
      </View>

      {/* 条件渲染：初始内容或聊天消息 */}
      {showInitialContent ? (
        <>


          {/* 共同标签区域 */}
          <View style={styles.tagsSection}>
            <Text style={styles.sectionTitle}>你们拥有的共同标签</Text>
            <View style={styles.tagsContainer}>
              {commonTags.map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{tag}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* 智能破冰话题卡片 */}
          <View style={styles.iceBreakCard}>
            <Text style={styles.iceBreakTitle}>智能破冰话题:</Text>

            <View style={styles.topicsList}>
              <Text style={styles.topicItem}>1. 听说你也想......(目标)，最近在用什么方法？我正苦恼......</Text>
              <Text style={styles.topicItem}>2. 你们单词APP用的墨墨还是扇贝？我老是记不住熟词僻义</Text>
              <Text style={styles.topicItem}>3. 兴趣:你最喜欢XX？我觉得...</Text>
              <Text style={styles.topicItem}>4. 用户标签含"拖延症",破冰话术:"我们都有拖延症！你试过番茄钟吗？我今天刚......"</Text>
            </View>
          </View>
        </>
      ) : (
        /* 聊天消息区域 */
        <View style={styles.chatMessagesContainer}>
          {messages.map((msg) => (
            <View key={msg.id} style={styles.messageContainer}>
              <View style={styles.messageContent}>
                <Text style={styles.messageText}>{msg.text}</Text>
              </View>
              <Image
                source={require('../../assets/Community_image/AvatarOne.png')}
                style={styles.messageAvatar}
              />
            </View>
          ))}
        </View>
      )}

      {/* 底部聊天框 */}
      <View style={styles.chatInputContainer}>
        <View style={styles.chatInputWrapper}>
          <TextInput
            style={styles.chatInput}
            placeholder="输入消息..."
            placeholderTextColor="#999"
            value={message}
            onChangeText={setMessage}
            onKeyPress={handleKeyPress}
            multiline={false}
            returnKeyType="send"
            onSubmitEditing={handleSendMessage}
          />
          <TouchableOpacity style={styles.addButton} onPress={handleSendMessage}>
            <Text style={styles.addButtonText}>+</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF8F3',
    paddingBottom: 80, // 为底部聊天框留出空间
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    paddingTop: 60,
  },
  backButton: {
    padding: 10,
  },
  settingsButton: {
    padding: 10,
  },
  icon: {
    width: 24,
    height: 24,
    tintColor: '#7A3C10',
  },
  menuIcon: {
    fontSize: 20,
    color: '#7A3C10',
    fontWeight: 'bold',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#7A3C10',
  },
  userConnection: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 30,
  },
  userAvatarContainer: {
    alignItems: 'center',
    width: 100,
  },
  avatarWrapper: {
    position: 'relative',
    marginBottom: 8,
  },
  avatar: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#F1E2FF',
  },
  userName: {
    fontSize: 14,
    color: '#7A3C10',
  },
  followButton: {
    position: 'absolute',
    bottom: -2,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#fff',
    borderWidth: 2,
    borderColor: '#7A3C10',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  followIcon: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#7A3C10',
    lineHeight: 14,
  },
  connectionLine: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 10,
  },
  line: {
    height: 2,
    width: 40,
    backgroundColor: '#7A3C10',
  },
  connectionDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#7A3C10',
  },
  tagsSection: {
    marginHorizontal: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#7A3C10',
    marginBottom: 12,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    backgroundColor: '#E8D5B7',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    borderWidth: 1,
    borderColor: '#7A3C10',
  },
  tagText: {
    fontSize: 14,
    color: '#7A3C10',
    fontWeight: '500',
  },
  iceBreakCard: {
    backgroundColor: '#FFEEDB',
    borderRadius: 12,
    padding: 20,
    marginHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iceBreakTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#7A3C10',
    marginBottom: 15,
  },
  topicsList: {
    marginBottom: 20,
  },
  topicItem: {
    fontSize: 14,
    color: '#7A3C10',
    lineHeight: 20,
    marginBottom: 12,
    paddingLeft: 8,
  },
  chatMessagesContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  messageContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    marginBottom: 12,
  },
  messageContent: {
    backgroundColor: '#7A3C10',
    borderRadius: 18,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginRight: 8,
    maxWidth: '70%',
  },
  messageText: {
    color: 'white',
    fontSize: 16,
    lineHeight: 20,
  },
  messageAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  chatInputContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFF8F3',
    paddingHorizontal: 20,
    paddingBottom: 34, // 安全距离
    paddingTop: 8,
  },
  chatInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    height: 40,
  },
  chatInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    paddingVertical: 0,
    paddingRight: 8,
    height: 28,
  },
  addButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#7A3C10',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 6,
  },
  addButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    lineHeight: 18,
  },



});

export default StudyroomMatch;