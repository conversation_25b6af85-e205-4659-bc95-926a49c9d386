// scripts/checkApiMigration.js - API迁移检查工具
const fs = require('fs');
const path = require('path');

// 需要检查的旧API导入
const OLD_API_IMPORTS = [
  "from '../lib/api'",
  "from '../../lib/api'",
  "from '../lib/contentApi'",
  "from '../../lib/contentApi'",
  "from '../lib/userProfile'",
  "from '../../lib/userProfile'",
  "from '../lib/profileApi'",
  "from '../../lib/profileApi'",
  "from '../constants/index'",
  "from '../../constants/index'"
];

// 需要检查的旧API函数调用
const OLD_API_FUNCTIONS = [
  'getIndexData(',
  'getFeaturedContentList(',
  'getHotCourseList(',
  'getLatestCourseList(',
  'getRecommendVideoList(',
  'saveUserTagAndGenerateProfile(',
  'sendSelectedIdsToBackend(',
  'login(',
  'register(',
  'verifyUser(',
  'getTagsByStepId('
];

/**
 * 递归扫描目录中的文件
 */
function scanDirectory(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      // 跳过不需要检查的目录
      if (!['node_modules', '.git', '.expo', 'dist', 'build', 'backup_old_api'].includes(file)) {
        scanDirectory(filePath, fileList);
      }
    } else if (file.endsWith('.js') || file.endsWith('.jsx') || file.endsWith('.ts') || file.endsWith('.tsx')) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

/**
 * 检查文件中的旧API使用
 */
function checkFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const issues = [];

  // 跳过不需要检查的文件
  const relativePath = path.relative(process.cwd(), filePath);
  const skipPatterns = [
    'scripts/',           // 跳过所有脚本文件
    'examples/',          // 跳过示例文件
    'lib/apiServices.js', // 跳过新API系统文件
    'lib/apiLegacy.js',
    'lib/apiClient.js',
    'lib/apiConfig.js',
    'backup_old_api/',    // 跳过备份文件
    'node_modules/',
    '.expo/',
    'dist/',
    'build/'
  ];

  // 特别跳过检查脚本本身
  if (relativePath.includes('checkApiMigration.js')) {
    return issues;
  }

  if (skipPatterns.some(pattern => relativePath.includes(pattern))) {
    return issues; // 返回空数组，跳过检查
  }

  // 检查旧的导入
  OLD_API_IMPORTS.forEach(importPattern => {
    if (content.includes(importPattern)) {
      issues.push({
        type: 'import',
        pattern: importPattern,
        suggestion: "使用 import apiServices from '../lib/apiServices'"
      });
    }
  });

  // 只检查函数调用，如果文件中有旧的导入语句
  const hasOldImports = OLD_API_IMPORTS.some(importPattern => content.includes(importPattern));

  if (hasOldImports) {
    // 只有当文件有旧导入时，才检查函数调用
    OLD_API_FUNCTIONS.forEach(funcPattern => {
      if (content.includes(funcPattern)) {
        const funcName = funcPattern.replace('(', '');
        let suggestion = '';

        // 提供具体的迁移建议
        if (funcName.includes('getIndex') || funcName.includes('Featured') ||
            funcName.includes('Hot') || funcName.includes('Latest') ||
            funcName.includes('Recommend')) {
          suggestion = `使用 apiServices.content.${funcName}`;
        } else if (funcName.includes('login') || funcName.includes('register') ||
                   funcName.includes('verify')) {
          suggestion = `使用 apiServices.auth.${funcName}`;
        } else if (funcName.includes('Profile') || funcName.includes('Tag')) {
          suggestion = `使用 apiServices.profile.${funcName}`;
        } else if (funcName.includes('getTagsByStepId')) {
          suggestion = `使用 apiServices.tag.${funcName}`;
        } else {
          suggestion = '查看 API_GUIDE.md 获取迁移指南';
        }

        issues.push({
          type: 'function',
          pattern: funcPattern,
          suggestion
        });
      }
    });
  }

  return issues;
}

/**
 * 主检查函数
 */
function checkApiMigration() {
  console.log('🔍 开始检查API迁移状态...\n');
  
  const projectRoot = path.resolve(__dirname, '..');
  const files = scanDirectory(projectRoot);
  
  let totalIssues = 0;
  const fileIssues = {};
  
  files.forEach(filePath => {
    const issues = checkFile(filePath);
    if (issues.length > 0) {
      const relativePath = path.relative(projectRoot, filePath);
      fileIssues[relativePath] = issues;
      totalIssues += issues.length;
    }
  });
  
  // 输出结果
  if (totalIssues === 0) {
    console.log('✅ 恭喜！没有发现需要迁移的旧API使用');
    console.log('📚 您可以考虑删除以下旧文件：');
    console.log('   - lib/api.js');
    console.log('   - lib/contentApi.js');
    console.log('   - lib/userProfile.js');
    console.log('   - lib/profileApi.js');
  } else {
    console.log(`⚠️  发现 ${totalIssues} 个需要迁移的API使用\n`);
    
    Object.entries(fileIssues).forEach(([filePath, issues]) => {
      console.log(`📁 ${filePath}:`);
      issues.forEach(issue => {
        console.log(`   ${issue.type === 'import' ? '📦' : '🔧'} ${issue.pattern}`);
        console.log(`      💡 建议: ${issue.suggestion}`);
      });
      console.log('');
    });
    
    console.log('📖 详细迁移指南请查看: API_GUIDE.md');
  }
  
  console.log('\n🔧 新API系统特性:');
  console.log('   ✨ 统一的配置管理');
  console.log('   🔄 自动重试机制');
  console.log('   🖼️  自动图片URL转换');
  console.log('   🔐 统一的认证管理');
  console.log('   📝 详细的调试日志');
  
  return { totalIssues, fileIssues };
}

// 如果直接运行此脚本
if (require.main === module) {
  checkApiMigration();
}

module.exports = { checkApiMigration };
