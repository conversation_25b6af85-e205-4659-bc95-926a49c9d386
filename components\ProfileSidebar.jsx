import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  SafeAreaView,
  Animated,
  Dimensions,
  Easing,
} from 'react-native';
import { useRouter } from 'expo-router';
import { shadowPresets } from '../utils/shadowUtils';

const { width: screenWidth } = Dimensions.get('window');

// 顶部添加 iconMap 静态映射
const iconMap = {
  'Star.png': require('../assets/Community_image/Star.png'),
  'Creat.png': require('../assets/Community_image/Creat.png'),
  'Publish.png': require('../assets/Community_image/Publish.png'),
  'Private.png': require('../assets/Community_image/Private.png'),
  'Practise.png': require('../assets/Community_image/Practise.png'),
};


const ProfileSidebar = ({ visible, onClose }) => {
  const router = useRouter();
  const slideAnim = useRef(new Animated.Value(-screenWidth * 0.8)).current;
  const [shouldRender, setShouldRender] = useState(visible);

  // 插值控制背景动画
  const backgroundTranslateX = slideAnim.interpolate({
    inputRange: [-screenWidth * 0.8, 0],
    outputRange: [-screenWidth * 0.2, 0],
  });

  const backgroundOpacity = slideAnim.interpolate({
    inputRange: [-screenWidth * 0.8, 0],
    outputRange: [0, 0.6],
  });

  useEffect(() => {
    if (visible) {
      setShouldRender(true);
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 350,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: -screenWidth * 0.8,
        duration: 280,
        easing: Easing.in(Easing.cubic),
        useNativeDriver: true,
      }).start(() => {
        setShouldRender(false); // 动画结束后才卸载
      });
    }
  }, [visible]);

  if (!shouldRender) return null;

  return (
    <SafeAreaView style={styles.overlay}>
      {/* 背景遮罩（支持联动动画） */}
      <Animated.View
        style={[
          styles.backdrop,
          {
            opacity: backgroundOpacity,
            transform: [{ translateX: backgroundTranslateX }],
          },
        ]}
      >
        <TouchableOpacity
          style={styles.backdropTouchable}
          onPress={onClose}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="关闭侧边栏"
        />
      </Animated.View>

      {/* 动画侧边栏 */}
      <Animated.View
        style={[
          styles.sidebar,
          {
            transform: [{ translateX: slideAnim }],
          },
        ]}
      >
        <View style={styles.gradientOverlay}>
          {/* 用户信息卡片 */}
          <View style={styles.card}>
            <Image source={require('../assets/Community_image/AvatarOne.png')} style={styles.avatar} />
            <Text style={styles.name}>Jessica</Text>

            <View style={styles.tags}>
              <Text style={styles.tag}>♀ 19岁</Text>
              <Text style={styles.tag}>浙江嘉兴</Text>
            </View>

            <View style={styles.stats}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>118</Text>
                <Text style={styles.statLabel}>关注</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>67</Text>
                <Text style={styles.statLabel}>粉丝</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>73</Text>
                <Text style={styles.statLabel}>获赞与收藏</Text>
              </View>
            </View>
          </View>

          {/* 菜单项 */}
          <View style={styles.menu}>
            {[
              ['社区等级', 'Star.png'],
              ['创作者中心', 'Creat.png'],
              ['我发布的', 'Publish.png'],
              ['我的收藏', 'Star.png'],
              ['隐私设置', 'Private.png'],
              ['我的练习', 'Practise.png'],
            ].map(([label, icon], idx) => (
              <TouchableOpacity
                key={idx}
                style={styles.menuTextItem}
                onPress={() => {
                  console.log('点击菜单项:', label);
                }}
                accessible={true}
                accessibilityRole="button"
                accessibilityLabel={label}
              >
                <Image source={iconMap[icon]} style={styles.menuIcon} />
                <Text style={styles.menuTextOnly}>{label}</Text>
              </TouchableOpacity>
            ))}
          </View>

        </View>
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 1)',
  },
  backdropTouchable: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  sidebar: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '80%',
    height: '100%',
    paddingTop: 60,
    paddingHorizontal: 16,
    overflow: 'hidden',
    backgroundColor: '#FFF8F3',
    ...shadowPresets.sidebar,
  },
  gradientOverlay: {
    flex: 1,
    paddingTop: 60,
    paddingHorizontal: 16,
    backgroundColor: 'transparent',
  },
  card: {
    backgroundColor: '#FFDDB4',
    borderRadius: 20,
    padding: 16,
    alignItems: 'center',
    marginBottom: 32,
    ...shadowPresets.card,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 10,
    resizeMode: 'cover',
  },
  name: {
    fontSize: 20,
    fontWeight: '600',
    color: '#222',
  },
  tags: {
    flexDirection: 'row',
    marginTop: 8,
  },
  tag: {
    backgroundColor: '#FFF',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    fontSize: 12,
    marginHorizontal: 4,
    color: '#666',
  },
  stats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
    width: '100%',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#222',
  },
  statLabel: {
    fontSize: 12,
    color: '#555',
  },
  menu: {
    flex: 1,
  },
  menuTextItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    marginVertical: 2,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  menuIcon: {
    width: 20,
    height: 20,
    marginRight: 12,
    resizeMode: 'contain',
  },
  menuTextOnly: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
});

export default ProfileSidebar;

