import { Stack } from "expo-router"
import { Colors } from "../constants/Colors"
import { useColorScheme } from "react-native"
import { StatusBar } from "expo-status-bar"
import { UserProvider } from "../contexts/UserContext"
import { GestureHandlerRootView } from 'react-native-gesture-handler';

export default function RootLayout() {
  const colorScheme = useColorScheme()
  const theme = Colors[colorScheme] ?? Colors.light

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <UserProvider>
        <StatusBar value="auto" />
        <Stack screenOptions={{
          headerStyle: { backgroundColor: theme.navBackground },
          headerTintColor: theme.title,
          headerShown: false, // 全局隐藏 header
        }}>
          {/* Groups */}
          <Stack.Screen name="(auth)" options={{ headerShown: false }} />
          <Stack.Screen name="(dashboard)" options={{ headerShown: false }} />

          {/* Individual Screens */}
          <Stack.Screen name="welcome" options={{ title: "Welcome", headerShown: false }} />
          <Stack.Screen name="profile-setup" options={{ title: "Profile Setup", headerShown: false }} />
          <Stack.Screen name="Chat" options={{ title: "Chat", headerShown: false }} />
          <Stack.Screen name="Chats" options={{ title: "Chats", headerShown: false }} />
          <Stack.Screen name="Today" options={{ title: "Today", headerShown: false }} />
          <Stack.Screen name="Community" options={{ title: "Community", headerShown: false }} />
        </Stack>
      </UserProvider>
    </GestureHandlerRootView>
  )
}