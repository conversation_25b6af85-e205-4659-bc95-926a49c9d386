import React, { useState } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, TextInput, Alert, Image, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as ImagePicker from 'expo-image-picker';
import ThemedView from '../components/ThemedView';
import ThemedText from '../components/ThemedText';
import { Colors } from '../constants/Colors';
import { postApi } from '../lib/apiServices';
import { STORAGE_KEYS } from '../lib/apiConfig';

const MorePostingPage = () => {
  const [content, setContent] = useState('');
  const [selectedTag, setSelectedTag] = useState('');
  const [isPublic, setIsPublic] = useState(true);
  const [friendsOnly, setFriendsOnly] = useState(false);
  const [selectedImages, setSelectedImages] = useState([]);
  const [isPublishing, setIsPublishing] = useState(false);

  const tags = ['我的搭子', '自习室', '职通车', '互助'];

  const handleBack = () => {
    router.back();
  };

  // 选择图片
  const handleSelectImage = async () => {
    try {
      // 请求权限
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('提示', '需要相册权限才能选择图片');
        return;
      }

      // 选择图片
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsMultipleSelection: true,
        quality: 0.8,
        aspect: [4, 3],
        allowsEditing: false,
      });

      if (!result.canceled && result.assets) {
        const newImages = result.assets.map(asset => ({
          uri: asset.uri,
          type: asset.type || 'image',
          fileName: asset.fileName || `image_${Date.now()}.jpg`,
          fileSize: asset.fileSize,
        }));

        // 限制最多9张图片
        const totalImages = selectedImages.length + newImages.length;
        if (totalImages > 9) {
          Alert.alert('提示', '最多只能选择9张图片');
          return;
        }

        setSelectedImages([...selectedImages, ...newImages]);
      }
    } catch (error) {
      console.error('选择图片失败:', error);
      Alert.alert('错误', '选择图片失败，请重试');
    }
  };

  // 移除图片
  const handleRemoveImage = (index) => {
    const newImages = selectedImages.filter((_, i) => i !== index);
    setSelectedImages(newImages);
  };

  // 发布帖子
  const handlePublish = async () => {
    if (!content.trim()) {
      Alert.alert('提示', '请输入内容');
      return;
    }

    setIsPublishing(true);

    try {
      // 导入所需模块
      const { authApi } = await import('../lib/apiServices');
      const apiClient = await import('../lib/apiClient');
      const AsyncStorage = await import('@react-native-async-storage/async-storage');

      // 检查登录状态和获取用户信息
      const loginStatus = await authApi.checkLoginStatus();
      const currentUserId = loginStatus.user?.id || loginStatus.user?.userAccount || null;

      // 准备发布数据 - 匹配数据库字段名
      const postData = {
        title: content.trim().substring(0, 50) || '新动态', // 从内容中提取标题，最多50字符
        userId: currentUserId, // 当前用户ID
        content: content.trim(),
        imageUrls: selectedImages.map(img => img.uri).join(','), // 将图片URI转换为逗号分隔的字符串
        tag: selectedTag, // 单个标签
        likeCount: 0, // 初始点赞数
        commentCount: 0, // 初始评论数
        viewCount: 0, // 初始浏览数
        visibility: isPublic ? 'public' : 'friends_only'
      };

      console.log('发布帖子数据:', postData);
      console.log('选择的标签:', selectedTag);
      console.log('选择的图片数量:', selectedImages.length);

      // 多种方式检查token
      console.log('=== Token检查开始 ===');

      // 方式1: 通过apiClient
      const token1 = await apiClient.default.getAuthToken();
      console.log('apiClient.getAuthToken():', token1 ? `${token1.substring(0, 20)}...` : 'null');

      // 方式2: 直接从AsyncStorage
      const token2 = await AsyncStorage.default.getItem(STORAGE_KEYS.AUTH_TOKEN);
      console.log(`AsyncStorage.getItem("${STORAGE_KEYS.AUTH_TOKEN}"):`, token2 ? `${token2.substring(0, 20)}...` : 'null');

      // 方式3: 检查所有存储的keys
      const allKeys = await AsyncStorage.default.getAllKeys();
      console.log('AsyncStorage中的所有keys:', allKeys);

      console.log('登录状态:', loginStatus);
      console.log('当前用户ID:', currentUserId);

      console.log('=== Token检查结束 ===');

      if (!loginStatus.isLoggedIn || !token1 || !currentUserId) {
        Alert.alert('提示', '请先登录后再发布帖子', [
          { text: '取消' },
          { text: '去登录', onPress: () => router.push('/login') }
        ]);
        return;
      }

      // 调用API发布帖子
      console.log('开始调用API发布帖子...');
      console.log('使用API端点: /api/community/post/create');
      console.log('发布数据:', postData);

      const result = await postApi.createPost(postData);

      console.log('发布成功:', result);

      Alert.alert('成功', '发布成功！', [
        {
          text: '确定',
          onPress: () => {
            try {
              if (router.canGoBack()) {
                router.back();
              } else {
                router.replace('/(dashboard)/community');
              }
            } catch (error) {
              console.log('导航错误，使用替代方案:', error);
              router.replace('/(dashboard)/community');
            }
          }
        }
      ]);

    } catch (error) {
      console.error('发布失败详细信息:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });

      let errorMessage = '网络错误，请重试';

      if (error.message) {
        if (error.message.includes('认证失败') || error.message.includes('401')) {
          errorMessage = '登录已过期，请重新登录';
          Alert.alert('登录过期', errorMessage, [
            { text: '取消' },
            {
              text: '重新登录',
              onPress: () => {
                try {
                  router.replace('/login');
                } catch (navError) {
                  console.log('导航到登录页面失败:', navError);
                  router.replace('/(auth)/login');
                }
              }
            }
          ]);
          return;
        } else if (error.message.includes('登录')) {
          errorMessage = '请先登录后再发布帖子';
        } else if (error.message.includes('网络')) {
          errorMessage = '网络连接失败，请检查网络后重试';
        } else if (error.message.includes('内容')) {
          errorMessage = '帖子内容不能为空';
        } else if (error.message.includes('API端点不存在')) {
          errorMessage = '服务暂时不可用，请稍后重试';
        } else {
          errorMessage = error.message;
        }
      }

      Alert.alert('发布失败', errorMessage);
    } finally {
      setIsPublishing(false);
    }
  };

  const toggleTag = (tag) => {
    // 如果点击的是已选中的标签，则取消选择
    if (selectedTag === tag) {
      setSelectedTag('');
    } else {
      // 否则选择新标签（只能选择一个）
      setSelectedTag(tag);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ThemedView style={styles.container}>
        {/* 头部导航 */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Image source={require('../assets/Arrows_left.png')} style={styles.backIcon} />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handlePublish}
            style={[styles.publishButton, isPublishing && styles.publishButtonDisabled]}
            disabled={isPublishing}
          >
            {isPublishing ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <ThemedText style={styles.publishText}>发布</ThemedText>
            )}
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* 白色内容盒子 - 只包含输入区域 */}
          <View style={styles.contentBox}>
            {/* 内容输入区域 */}
            <TextInput
              style={styles.contentInput}
              placeholder="分享新鲜事......"
              value={content}
              onChangeText={setContent}
              multiline
              textAlignVertical="top"
              maxLength={200}
            />

            {/* 添加图片按钮和字数统计 */}
            <View style={styles.bottomRow}>
              <TouchableOpacity
                style={styles.addImageButton}
                onPress={handleSelectImage}
              >
                <Image source={require('../assets/More_image/More.png')} style={styles.addImageIcon} />
                <ThemedText style={styles.addImageText}>添加图片</ThemedText>
              </TouchableOpacity>

              <View style={styles.charCountContainer}>
                <ThemedText style={styles.charCount}>{content.length}/200</ThemedText>
              </View>
            </View>

            {/* 图片预览区域 */}
            {selectedImages.length > 0 && (
              <View style={styles.imagePreviewContainer}>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  {selectedImages.map((image, index) => (
                    <View key={index} style={styles.imagePreviewItem}>
                      <Image source={{ uri: image.uri }} style={styles.previewImage} />
                      <TouchableOpacity
                        style={styles.removeImageButton}
                        onPress={() => handleRemoveImage(index)}
                      >
                        <ThemedText style={styles.removeImageText}>×</ThemedText>
                      </TouchableOpacity>
                    </View>
                  ))}
                </ScrollView>
                <ThemedText style={styles.imageCountText}>
                  {selectedImages.length}/9 张图片
                </ThemedText>
              </View>
            )}
          </View>

          {/* 标签选择 */}
          <View style={styles.tagsSection}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tagsContainer}>
              {tags.map((tag, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.tagItem,
                    selectedTag === tag && styles.selectedTag
                  ]}
                  onPress={() => toggleTag(tag)}
                >
                  <Image
                    source={require('../assets/More_image/Well_number.png')}
                    style={selectedTag === tag ? styles.selectedTagPrefix : styles.tagPrefix}
                  />
                  <ThemedText style={[
                    styles.tagText,
                    selectedTag === tag && styles.selectedTagText
                  ]}>
                    {tag}
                  </ThemedText>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          {/* 可见性设置 */}
          <View style={styles.visibilitySection}>
            <TouchableOpacity
              style={styles.visibilityOption}
              onPress={() => {
                setIsPublic(true);
                setFriendsOnly(false);
              }}
            >
              <View style={[styles.checkbox, isPublic && styles.checkedBox]}>
                {isPublic && <ThemedText style={styles.checkmark}>✓</ThemedText>}
              </View>
              <ThemedText style={styles.visibilityText}>公开</ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.visibilityOption}
              onPress={() => {
                setIsPublic(false);
                setFriendsOnly(true);
              }}
            >
              <View style={[styles.checkbox, friendsOnly && styles.checkedBox]}>
                {friendsOnly && <ThemedText style={styles.checkmark}>✓</ThemedText>}
              </View>
              <ThemedText style={styles.visibilityText}>仅好友可见</ThemedText>
            </TouchableOpacity>
          </View>
        </ScrollView>

        {/* 底部装饰花朵图案 */}
        <View style={styles.bottomDecoration}>
          <Image
            source={require('../assets/More_image/flower.png')}
            style={styles.flowerPattern}
            resizeMode="cover"
          />
        </View>
      </ThemedView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF8F3',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: 'transparent',
  },
  backButton: {
    padding: 5,
  },
  backIcon: {
    width: 24,
    height: 24,
    tintColor: '#666',
  },
  publishButton: {
    backgroundColor: '#FFC47B',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    minWidth: 80,
    alignItems: 'center',
    justifyContent: 'center',
  },
  publishButtonDisabled: {
    backgroundColor: '#ccc',
    opacity: 0.7,
  },
  publishText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  contentBox: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginTop: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  contentInput: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    minHeight: 120,
    textAlignVertical: 'top',
    marginBottom: 20,
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  addImageButton: {
    width: 100,
    height: 80,
    backgroundColor: '#F5E6D3',
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 8,
  },
  addImageIcon: {
    width: 20,
    height: 20,
    tintColor: '#D4A574',
    marginBottom: 4,
  },
  addImageText: {
    fontSize: 12,
    color: '#D4A574',
    fontWeight: '500',
  },
  charCountContainer: {
    alignItems: 'flex-end',
  },
  charCount: {
    fontSize: 14,
    color: '#999',
  },
  tagsSection: {
    marginBottom: 20,
  },
  tagsContainer: {
    flexDirection: 'row',
  },
  tagItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFEEDB',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 12,
  },
  selectedTag: {
    backgroundColor: '#FFD29B',
  },
  tagPrefix: {
    width: 16,
    height: 16,
    tintColor: '#FFB87E',
    marginRight: 4,
  },
  selectedTagPrefix: {
    width: 16,
    height: 16,
    tintColor: '#7A3C10',
    marginRight: 4,
  },
  tagText: {
    fontSize: 14,
    color: '#FFB87E',
    fontWeight: '500',
  },
  selectedTagText: {
    color: '#7A3C10',
  },
  visibilitySection: {
    marginBottom: 40,
  },
  visibilityOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#ddd',
    borderRadius: 4,
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkedBox: {
    backgroundColor: '#FFB366',
    borderColor: '#FFB366',
  },
  checkmark: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  visibilityText: {
    fontSize: 16,
    color: '#333',
  },
  bottomDecoration: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 120,
    overflow: 'hidden',
  },
  flowerPattern: {
    width: '100%',
    height: '100%',
    opacity: 0.6,
  },
  // 图片预览样式
  imagePreviewContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  imagePreviewItem: {
    position: 'relative',
    marginRight: 12,
  },
  previewImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#ff4444',
    alignItems: 'center',
    justifyContent: 'center',
  },
  removeImageText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    lineHeight: 16,
  },
  imageCountText: {
    fontSize: 12,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
  },
});

export default MorePostingPage;
