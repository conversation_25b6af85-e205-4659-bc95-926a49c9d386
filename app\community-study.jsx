import React, { useEffect } from 'react';
import { StyleSheet, View, Image, Text, TouchableOpacity, ScrollView, ImageBackground } from 'react-native';
import ThemedView from '../components/ThemedView';
import ThemedText from '../components/ThemedText';
import { useRouter } from 'expo-router';
import { Colors } from '../constants/Colors';

export default function CommunityStudy() {
  const router = useRouter();

  // 清理函数，确保在组件卸载时清理状态
  useEffect(() => {
    return () => {
      // 清理任何可能的焦点状态
    };
  }, []);

  return (

    <ScrollView style={{ flex: 1, backgroundColor: '#FFF8F3' }} showsVerticalScrollIndicator={false}>
      {/* 顶部标题栏 */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => router.push('/(dashboard)/community?tab=community&filterTag=自习室')}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="返回社区页面"
        >
          <Image source={require('../assets/FrameTwo.png')} style={[styles.backIcon]} />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>自习室</ThemedText>
        <TouchableOpacity
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="更多选项"
        >
          <ThemedText style={styles.headerMenu}>⋯</ThemedText>
        </TouchableOpacity>
      </View>
      {/* 统计区 */}
      <View style={styles.statsRow}>
        <Image source={require('../assets/standdog.png')} style={styles.statsDog} />
        <View style={{ flex: 1 }}>
          <ThemedText style={styles.statsText}>自习时天数：<Text style={styles.statsNum}>91</Text>天</ThemedText>
          <ThemedText style={styles.statsText}>当前连续自习：<Text style={styles.statsNum}>39</Text>天</ThemedText>
          <ThemedText style={styles.statsText}>累计专注时间：<Text style={styles.statsNum}>881h</Text></ThemedText>
        </View>
        <Image source={require('../assets/Plan_image/Sun.png')} style={styles.statsSun} />
      </View>

      {/* 功能按钮区 */}
      <View style={styles.funcRow}>
        <TouchableOpacity
          style={[styles.funcBtnSquare, styles.funcBtnMusic]}
          onPress={() => {
            console.log('点击音乐陪伴，跳转到Music页面');
            router.push('/Community/Music');
          }}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="音乐陪伴"
        >
          <ThemedText style={styles.funcBtnText}>音乐陪伴</ThemedText>
          <Image source={require('../assets/Community_image/Music.png')} style={styles.funcBtnImgOverflow} />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.funcBtnSquare, styles.funcBtnStudyRoom]}
          onPress={() => {
            router.push('/Community/StudyroomCreate');
          }}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="新建自习室"
        >
          <ThemedText style={styles.funcBtnText}>新建自习室</ThemedText>
          <Image source={require('../assets/Community_image/Create.png')} style={styles.funcBtnImgOverflow} />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.funcBtnSquare, styles.funcBtnHomework]}
          onPress={() => {
            // TODO: 跳转到作业无忧页面
            console.log('点击作业无忧');
          }}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="作业无忧"
        >
          <ThemedText style={styles.funcBtnText}>作业无忧</ThemedText>
          <Image source={require('../assets/Community_image/HandsomeAvatar.png')} style={styles.funcBtnImgOverflow} />
        </TouchableOpacity>
      </View>
      {/* 自习室列表 */}
      <View style={{ paddingBottom: 20 }}>
        <ThemedText style={{ fontSize: 20, fontWeight: 'bold', color: '#222', marginLeft: 24, marginBottom: 16 }}>自习室</ThemedText>
        {/* 我的自习室卡片 */}
        <TouchableOpacity
          style={styles.studyCard}
          onPress={() => router.push('/Community/StudyroomA')}
        >
          <ImageBackground
            source={require('../assets/Community_image/BackgroundOne.png')}
            style={styles.cardBackground}
            imageStyle={styles.cardBackgroundImage}
          >
            {/* 透明遮罩层 */}
            <View style={styles.overlay} />

            {/* 右上角叠加头像 */}
            <View style={styles.avatarStack}>
              <Image source={require('../assets/Community_image/AOne.png')} style={[styles.stackAvatar, styles.avatar1]} />
              <Image source={require('../assets/Community_image/ATwo.png')} style={[styles.stackAvatar, styles.avatar2]} />
              <Image source={require('../assets/Community_image/AThree.png')} style={[styles.stackAvatar, styles.avatar3]} />
              <View style={[styles.stackAvatar, styles.avatar4, styles.countCircle]}>
                <Text style={styles.countText}>+6</Text>
              </View>
            </View>

            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <View style={{ flex: 1 }}>
                <View style={styles.titleRow}>
                  <ThemedText style={styles.studyCardTitleWhite}>我的自习室</ThemedText>
                  <Image source={require('../assets/Community_image/L.png')} style={styles.titleIcon} />
                  <ThemedText style={styles.statusText}>217自学中...</ThemedText>
                </View>
                <View style={styles.tagContainer}>
                  <View style={styles.tag}>
                    <ThemedText style={styles.tagText}>考研</ThemedText>
                  </View>
                  <View style={styles.tag}>
                    <ThemedText style={styles.tagText}>大一</ThemedText>
                  </View>
                </View>
                <ThemedText style={styles.studyCardTimeWhite}>自习时间 | 18:00-23:00</ThemedText>
              </View>
              <View style={{ flexDirection: 'column', justifyContent: 'flex-end', alignItems: 'flex-end', flex: 0, marginTop: 20 }}>
                <TouchableOpacity onPress={() => { /* TODO: 分享功能 */ }} style={styles.shareButton}>
                  <Image source={require('../assets/Community_image/SelfSrudyShare.png')} style={styles.studyCardShare} />
                </TouchableOpacity>
              </View>
            </View>
          </ImageBackground>
        </TouchableOpacity>
        {/* 其他自习室卡片 */}
        <TouchableOpacity
          style={styles.studyCard}
          onPress={() => router.push('/Community/StudyroomA')}
        >
          <ImageBackground
            source={require('../assets/Community_image/BackgroundTwo.png')}
            style={styles.cardBackground}
            imageStyle={styles.cardBackgroundImage}
          >
            {/* 透明遮罩层 */}
            <View style={styles.overlay} />

            {/* 右上角叠加头像 */}
            <View style={styles.avatarStack}>
              <Image source={require('../assets/Community_image/AOne.png')} style={[styles.stackAvatar, styles.avatar1]} />
              <Image source={require('../assets/Community_image/ATwo.png')} style={[styles.stackAvatar, styles.avatar2]} />
              <Image source={require('../assets/Community_image/AThree.png')} style={[styles.stackAvatar, styles.avatar3]} />
              <View style={[styles.stackAvatar, styles.avatar4, styles.countCircle]}>
                <Text style={styles.countText}>+6</Text>
              </View>
            </View>

            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <View style={{ flex: 1 }}>
                <View style={styles.titleRow}>
                  <ThemedText style={styles.studyCardTitleWhite}>Caaary建的自习室</ThemedText>
                  <Image source={require('../assets/Community_image/L.png')} style={styles.titleIcon} />
                  <ThemedText style={styles.statusText}>217自学中...</ThemedText>
                </View>
                <View style={styles.tagContainer}>
                  <View style={styles.tag}>
                    <ThemedText style={styles.tagText}>日常学习</ThemedText>
                  </View>
                  <View style={styles.tag}>
                    <ThemedText style={styles.tagText}>研究生日常</ThemedText>
                  </View>
                </View>
                <ThemedText style={styles.studyCardTimeWhite}>自习时间 | 18:30-21:00</ThemedText>
              </View>
              <View style={{ flexDirection: 'column', justifyContent: 'flex-end', alignItems: 'flex-end', flex: 0, marginTop: 20 }}>
                <TouchableOpacity onPress={() => { /* TODO: 分享功能 */ }} style={styles.shareButton}>
                  <Image source={require('../assets/Community_image/SelfSrudyShare.png')} style={styles.studyCardShare} />
                </TouchableOpacity>
              </View>
            </View>
          </ImageBackground>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.studyCard}
          onPress={() => router.push('/Community/StudyroomA')}
        >
          <ImageBackground
            source={require('../assets/Community_image/BackgroundThree.png')}
            style={styles.cardBackground}
            imageStyle={styles.cardBackgroundImage}
          >
            {/* 透明遮罩层 */}
            <View style={styles.overlay} />

            {/* 右上角叠加头像 */}
            <View style={styles.avatarStack}>
              <Image source={require('../assets/Community_image/AOne.png')} style={[styles.stackAvatar, styles.avatar1]} />
              <Image source={require('../assets/Community_image/ATwo.png')} style={[styles.stackAvatar, styles.avatar2]} />
              <Image source={require('../assets/Community_image/AThree.png')} style={[styles.stackAvatar, styles.avatar3]} />
              <View style={[styles.stackAvatar, styles.avatar4, styles.countCircle]}>
                <Text style={styles.countText}>+6</Text>
              </View>
            </View>

            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <View style={{ flex: 1 }}>
                <View style={styles.titleRow}>
                  <ThemedText style={styles.studyCardTitleWhite}>Caaary建的自习室</ThemedText>
                  <Image source={require('../assets/Community_image/L.png')} style={styles.titleIcon} />
                  <ThemedText style={styles.statusText}>217自学中...</ThemedText>
                </View>
                <View style={styles.tagContainer}>
                  <View style={styles.tag}>
                    <ThemedText style={styles.tagText}>日常学习</ThemedText>
                  </View>
                  <View style={styles.tag}>
                    <ThemedText style={styles.tagText}>研究生日常</ThemedText>
                  </View>
                </View>
                <ThemedText style={styles.studyCardTimeWhite}>自习时间 | 18:30-21:00</ThemedText>
              </View>
              <View style={{ flexDirection: 'column', justifyContent: 'flex-end', alignItems: 'flex-end', flex: 0, marginTop: 20 }}>
                <TouchableOpacity onPress={() => { /* TODO: 分享功能 */ }} style={styles.shareButton}>
                  <Image source={require('../assets/Community_image/SelfSrudyShare.png')} style={styles.studyCardShare} />
                </TouchableOpacity>
              </View>
            </View>
          </ImageBackground>
        </TouchableOpacity>
      </View>
    </ScrollView >

  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingHorizontal: 16,
    backgroundColor: 'rgba(255, 248, 243, 0.95)',
    backdropFilter: 'blur(10px)',
    marginBottom: 24
  },
  backIcon: { width: 28, height: 28, marginRight: 8 },
  headerTitle: { flex: 1, fontSize: 22, fontWeight: 'bold', color: '#222', textAlign: 'center' },
  headerMenu: { fontSize: 24, color: '#bbb' },
  statsRow: { flexDirection: 'row', alignItems: 'center', marginHorizontal: 16, marginBottom: 18 },
  statsDog: { width: 90, height: 90, marginRight: 16 },
  statsText: { fontSize: 15, color: '#222', marginBottom: 2 },
  statsNum: { fontWeight: 'bold', color: '#888' },
  statsSun: { width: 54, height: 54, marginLeft: 12 },
  funcRow: { flexDirection: 'row', justifyContent: 'space-between', marginHorizontal: 16, marginBottom: 18 },
  funcBtnSquare: { flex: 1, backgroundColor: Colors.light.uiBackground, borderRadius: 16, alignItems: 'center', justifyContent: 'space-between', aspectRatio: 1, marginHorizontal: 4, paddingVertical: 12, overflow: 'visible' },
  funcBtnMusic: { backgroundColor: '#ffcda4ff' },
  funcBtnStudyRoom: { backgroundColor: '#FED8CE' },
  funcBtnHomework: { backgroundColor: '#DCECF9' },
  funcBtnText: { fontSize: 16, color: '#33333', marginTop: 0 },
  funcBtnImg: { width: '80%', height: undefined, aspectRatio: 1, marginTop: 'auto', marginBottom: 8, borderRadius: 12 },
  funcBtnImgOverflow: {
    width: 60,
    height: 60,
    position: 'absolute',
    top: 50,
    right: 10,
    zIndex: 10,
    resizeMode: 'contain',
  },
  studyCard: {
    borderRadius: 18,
    marginHorizontal: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOpacity: 0.04,
    shadowRadius: 8,
    height: 120, // 固定高度，适合手机端
    overflow: 'hidden'
  },
  cardBackground: {
    width: '100%',
    height: 120, // 固定高度
    padding: 16, // 减少内边距
  },
  cardBackgroundImage: {
    borderRadius: 18,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.25)',
    borderRadius: 18,
  },
  avatarStack: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 80,
    height: 32,
    zIndex: 10,
  },
  stackAvatar: {
    position: 'absolute',
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#fff',
  },
  avatar1: {
    left: 0,
    top: 0,
    zIndex: 1,
  },
  avatar2: {
    left: 16,
    top: 0,
    zIndex: 2,
  },
  avatar3: {
    left: 32,
    top: 0,
    zIndex: 3,
  },
  avatar4: {
    left: 48,
    top: 0,
    zIndex: 4,
  },
  countCircle: {
    backgroundColor: '#FFD29B',
    justifyContent: 'center',
    alignItems: 'center',
  },
  countText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#333',
  },
  studyCardTitle: { fontSize: 16, fontWeight: 'bold', color: '#222', marginBottom: 2, marginTop: 0 },
  studyCardTitleWhite: { fontSize: 16, fontWeight: 'bold', color: '#fff', marginBottom: 4, marginTop: 0 },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  titleIcon: {
    width: 16,
    height: 16,
    marginLeft: 8,
  },
  statusText: {
    fontSize: 12,
    color: '#fff',
    opacity: 0.8,
    marginLeft: 8,
  },
  studyCardDesc: { fontSize: 13, color: '#888', marginBottom: 8 },
  studyCardTime: { fontSize: 13, color: '#aaa', marginBottom: 8, marginTop: 16 },
  studyCardTimeWhite: { fontSize: 13, color: '#fff', marginBottom: 8, marginTop: 8 },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12,
    gap: 8,
  },
  tag: {
    backgroundColor: '#FFD29B',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  tagText: {
    fontSize: 10,
    color: '#333',
    fontWeight: '500',
  },
  shareButton: {
    backgroundColor: '#FFD29B',
    borderRadius: 18,
    padding: 8,
  },
  studyCardDog: { width: 80, height: 80, borderRadius: 27, marginRight: 60 },
  studyCardArrowWrap: { backgroundColor: '#F2F2F2', borderRadius: 20, padding: 8 },
  studyCardArrow: { width: 24, height: 24 },
  studyCardShare: { width: 20, height: 20, marginTop: 0, marginRight: 0 },
});