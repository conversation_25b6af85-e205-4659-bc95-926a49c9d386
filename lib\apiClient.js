// lib/apiClient.js - 统一的API客户端
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getApiBaseUrl, getAuthApiUrl, getImageBaseUrl, STORAGE_KEYS } from './apiConfig';
import { transformArrayImageUrls, transformObjectImageUrls } from './imageUtils';

/**
 * 统一的HTTP客户端类
 */
class ApiClient {
  constructor() {
    this.defaultTimeout = 15000; // 15秒超时
    this.maxRetries = 2; // 最大重试次数
  }

  /**
   * 获取认证token
   */
  async getAuthToken() {
    try {
      return await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    } catch (error) {
      console.error(`[${Platform.OS}] 获取token失败:`, error);
      return null;
    }
  }

  /**
   * 保存认证token
   */
  async saveAuthToken(token) {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
      console.log(`[${Platform.OS}] Token已保存`);
      return true;
    } catch (error) {
      console.error(`[${Platform.OS}] 保存token失败:`, error);
      return false;
    }
  }

  /**
   * 移除认证token
   */
  async removeAuthToken() {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      console.log(`[${Platform.OS}] Token已移除`);
      return true;
    } catch (error) {
      console.error(`[${Platform.OS}] 移除token失败:`, error);
      return false;
    }
  }

  /**
   * 通用存储方法
   */
  get storage() {
    return {
      async setItem(key, value) {
        return await AsyncStorage.setItem(key, value);
      },
      async getItem(key) {
        return await AsyncStorage.getItem(key);
      },
      async removeItem(key) {
        return await AsyncStorage.removeItem(key);
      }
    };
  }

  /**
   * 网络连接测试
   * @param {string} testUrl - 测试URL（可选）
   * @returns {Promise<boolean>} 连接是否成功
   */
  async testConnection(testUrl = null) {
    const url = testUrl || await getBaseApiUrl() + '/test';

    try {
      console.log(`[${Platform.OS}] 测试网络连接:`, url);

      const response = await this.request(url, {
        method: 'GET',
        requireAuth: false,
        timeout: 5000
      });

      console.log(`[${Platform.OS}] 网络连接测试成功`);
      return response.ok;
    } catch (error) {
      console.error(`[${Platform.OS}] 网络连接测试失败:`, error);
      return false;
    }
  }

  /**
   * 发起HTTP请求的核心方法
   */
  async request(url, options = {}, retryCount = 0) {
    const {
      method = 'GET',
      headers = {},
      body,
      timeout = this.defaultTimeout,
      requireAuth = true,
      transformImages = false,
      imageFields = ['coverUrl', 'imageUrl', 'avatarUrl', 'thumbnailUrl']
    } = options;

    try {
      console.log(`[${Platform.OS}] 发起请求 (尝试 ${retryCount + 1}/${this.maxRetries + 1}):`, {
        url,
        method,
        timeout: `${timeout}ms`
      });

      // 准备请求头
      const requestHeaders = {
        'Content-Type': 'application/json',
        ...headers
      };

      // 添加认证token
      if (requireAuth) {
        const token = await this.getAuthToken();
        console.log(`[${Platform.OS}] requireAuth=true, token状态:`, token ? `${token.substring(0, 20)}...` : 'null');

        if (token) {
          requestHeaders.Authorization = `Bearer ${token}`;
          console.log(`[${Platform.OS}] 已添加Authorization头部`);
        } else {
          console.error(`[${Platform.OS}] 需要认证但未找到token`);
          throw new Error('认证token不存在，请重新登录');
        }
      } else {
        console.log(`[${Platform.OS}] 请求不需要认证 (requireAuth=false)`);
      }

      // 创建超时控制器
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        console.log(`[${Platform.OS}] 请求超时，取消请求`);
        controller.abort();
      }, timeout);

      const startTime = Date.now();

      // 发起请求
      const response = await fetch(url, {
        method,
        headers: requestHeaders,
        body: body ? JSON.stringify(body) : undefined,
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log(`[${Platform.OS}] 响应成功 (耗时 ${duration}ms):`, {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      });

      // 处理401未授权
      if (response.status === 401) {
        console.warn(`[${Platform.OS}] Token可能已过期，需要重新登录`);
        // 可以在这里添加自动登出逻辑
      }

      // 解析响应
      let data;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text();
      }

      console.log(`[${Platform.OS}] 原始API响应:`, {
        status: response.status,
        dataType: typeof data,
        hasCode: data && typeof data === 'object' && 'code' in data,
        hasData: data && typeof data === 'object' && 'data' in data
      });

      // 转换图片URL
      if (transformImages && data) {
        // 处理标准API返回结构中的图片URL
        if (data && typeof data === 'object' && data.data) {
          // 如果是标准结构 { code: 0, data: {...} }，转换 data 部分的图片
          if (Array.isArray(data.data)) {
            data.data = transformArrayImageUrls(data.data, imageFields);
          } else if (typeof data.data === 'object') {
            data.data = transformObjectImageUrls(data.data, imageFields);
          }
        } else if (Array.isArray(data)) {
          data = transformArrayImageUrls(data, imageFields);
        } else if (typeof data === 'object') {
          data = transformObjectImageUrls(data, imageFields);
        }
      }

      return {
        ok: response.ok,
        status: response.status,
        statusText: response.statusText,
        data,
        headers: response.headers
      };

    } catch (error) {
      console.error(`[${Platform.OS}] 请求错误 (尝试 ${retryCount + 1}/${this.maxRetries + 1}):`, {
        message: error.message,
        name: error.name,
        url
      });

      // 检查是否应该重试
      const shouldRetry = retryCount < this.maxRetries && (
        error.name === 'AbortError' || // 超时
        error.message.includes('Network request failed') ||
        error.message.includes('timeout') ||
        error.message.includes('fetch')
      );

      if (shouldRetry) {
        console.log(`[${Platform.OS}] 准备重试请求，等待 ${(retryCount + 1) * 1000}ms...`);
        await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 1000));
        return this.request(url, options, retryCount + 1);
      }

      // 提供详细的错误信息
      if (error.message.includes('Network request failed') ||
        error.message.includes('fetch') ||
        error.message.includes('timeout') ||
        error.name === 'AbortError') {
        console.error(`[${Platform.OS}] 网络连接问题，请检查：`);
        console.error('1. 后端服务是否正在运行');
        console.error('2. IP地址配置是否正确');
        console.error('3. 手机和开发机器是否在同一网络');
        console.error('4. 防火墙是否阻止了连接');
      }

      throw error;
    }
  }

  /**
   * GET请求
   */
  async get(endpoint, options = {}) {
    const baseUrl = await getApiBaseUrl();
    const url = `${baseUrl}${endpoint}`;
    return this.request(url, { ...options, method: 'GET' });
  }

  /**
   * POST请求
   */
  async post(endpoint, data, options = {}) {
    const baseUrl = await getApiBaseUrl();
    const url = `${baseUrl}${endpoint}`;
    return this.request(url, { ...options, method: 'POST', body: data });
  }

  /**
   * PUT请求
   */
  async put(endpoint, data, options = {}) {
    const baseUrl = await getApiBaseUrl();
    const url = `${baseUrl}${endpoint}`;
    return this.request(url, { ...options, method: 'PUT', body: data });
  }

  /**
   * DELETE请求
   */
  async delete(endpoint, options = {}) {
    const baseUrl = await getApiBaseUrl();
    const url = `${baseUrl}${endpoint}`;
    return this.request(url, { ...options, method: 'DELETE' });
  }

  /**
   * 认证相关的请求（使用认证API URL）
   */
  async authRequest(endpoint, options = {}) {
    const authUrl = await getAuthApiUrl();
    const url = `${authUrl}${endpoint}`;

    // 默认认证请求不需要token，除非明确指定
    const defaultOptions = { requireAuth: false, ...options };
    return this.request(url, defaultOptions);
  }
}

// 创建单例实例
const apiClient = new ApiClient();

export default apiClient;
