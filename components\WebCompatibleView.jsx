import React, { useState, useEffect } from 'react';
import { View, Platform, Dimensions } from 'react-native';

/**
 * Web兼容视图组件
 * 解决Web端初始加载时尺寸不正确的问题
 */
const WebCompatibleView = ({ 
  children, 
  style, 
  fallbackStyle,
  initialDelay = 100,
  ...props 
}) => {
  const [isReady, setIsReady] = useState(Platform.OS !== 'web');
  const [dimensions, setDimensions] = useState(() => {
    if (Platform.OS === 'web') {
      // Web端初始使用安全的默认值
      return {
        width: typeof window !== 'undefined' ? window.innerWidth : 1200,
        height: typeof window !== 'undefined' ? window.innerHeight : 800
      };
    }
    return Dimensions.get('window');
  });

  useEffect(() => {
    if (Platform.OS === 'web') {
      // 延迟初始化，确保DOM完全加载
      const initTimer = setTimeout(() => {
        setDimensions({
          width: window.innerWidth,
          height: window.innerHeight
        });
        setIsReady(true);
      }, initialDelay);

      // 监听窗口大小变化
      const handleResize = () => {
        setDimensions({
          width: window.innerWidth,
          height: window.innerHeight
        });
      };

      // 使用原生事件监听器，更可靠
      window.addEventListener('resize', handleResize);
      
      // 监听DOM内容加载完成
      const handleDOMContentLoaded = () => {
        setDimensions({
          width: window.innerWidth,
          height: window.innerHeight
        });
        setIsReady(true);
      };

      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', handleDOMContentLoaded);
      } else {
        // DOM已经加载完成
        handleDOMContentLoaded();
      }

      return () => {
        clearTimeout(initTimer);
        window.removeEventListener('resize', handleResize);
        document.removeEventListener('DOMContentLoaded', handleDOMContentLoaded);
      };
    }
  }, [initialDelay]);

  // Web端未准备好时的样式 - 移除缩放动画
  const webNotReadyStyle = Platform.OS === 'web' && !isReady ? {
    opacity: 0,
    // 移除 transform: [{ scale: 0.95 }] 以消除缩放动画
    ...fallbackStyle
  } : {};

  // Web端准备好后的样式 - 移除缩放动画
  const webReadyStyle = Platform.OS === 'web' && isReady ? {
    opacity: 1,
    // 移除 transform: [{ scale: 1 }] 以消除缩放动画
    transition: 'opacity 0.3s ease', // 只保留透明度动画
    minHeight: '100vh',
    width: '100%',
    maxWidth: dimensions.width > 768 ? '1200px' : '100%',
    marginLeft: 'auto',
    marginRight: 'auto'
  } : {};

  const combinedStyle = [
    style,
    webNotReadyStyle,
    webReadyStyle
  ];

  return (
    <View 
      style={combinedStyle}
      {...props}
    >
      {children}
    </View>
  );
};

export default WebCompatibleView;
