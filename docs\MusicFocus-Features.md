# MusicFocus 专注模式功能说明

## 🎯 功能概述

MusicFocus.jsx 现在包含完整的专注模式功能，提供沉浸式的学习体验。

## ✨ 新增功能

### 1. 图片自动切换
- ✅ **10秒自动切换** - 播放状态下每10秒自动切换背景图片
- ✅ **手动切换** - 点击底部圆点指示器手动切换图片
- ✅ **平滑动画** - 图片切换带有平滑的滚动动画

### 2. 心电图特效
- ✅ **动态波形** - 播放时显示移动的心电图波形
- ✅ **心跳动画** - 模拟真实心跳的脉冲效果
- ✅ **脉冲指示器** - 右侧小圆点同步心跳节奏
- ✅ **发光效果** - 绿色心电图线条，营造专注氛围

### 3. 专注时间计时
- ✅ **实时计时** - 显示当前专注时间（分:秒格式）
- ✅ **自动计时** - 播放时自动开始计时，暂停时停止
- ✅ **时间显示** - 右上角显示专注时间

### 4. 播放控制
- ✅ **播放/暂停** - 点击播放键开始/停止专注模式
- ✅ **状态同步** - 播放时启动所有动画和计时器
- ✅ **图标切换** - 播放/暂停图标自动切换

## 🎮 操作说明

### 基本操作
```
播放控制: 点击中央播放按钮 ▶/⏸
图片切换: 点击底部圆点指示器 ● ● ●
自动切换: 播放时每10秒自动切换图片
专注计时: 播放时自动开始计时
心电图: 播放时显示动态心电图特效
```

### 功能状态
- **停止状态**: 显示静态图片，无动画，计时器暂停
- **播放状态**: 自动切换图片，心电图动画，计时器运行

## 🔧 技术实现

### 核心功能
```javascript
// 自动切换定时器
useEffect(() => {
  if (isPlaying) {
    autoSwitchTimerRef.current = setInterval(() => {
      autoSwitchImage();
    }, 10000); // 10秒切换
  }
}, [isPlaying]);

// 心电图动画
useEffect(() => {
  if (isPlaying) {
    // 心跳脉冲动画
    const createHeartbeat = () => { /* ... */ };
    // 波形移动动画
    const createWave = () => { /* ... */ };
  }
}, [isPlaying]);

// 专注时间计时
useEffect(() => {
  if (isPlaying) {
    focusTimerRef.current = setInterval(() => {
      setFocusTime(prev => prev + 1);
    }, 1000);
  }
}, [isPlaying]);
```

### 动画效果
- **心电图波形**: `translateX` + `scaleY` 变换
- **脉冲效果**: 周期性的 `scale` 动画
- **图片切换**: `ScrollView` 的平滑滚动

## 🎨 UI组件

### 新增UI元素
1. **专注时间显示** - 右上角时间计时器
2. **心电图容器** - 播放按钮上方的心电图特效
3. **可点击指示器** - 底部圆点支持点击切换
4. **动画波形** - 绿色心电图线条和脉冲点

### 样式配置
```javascript
// 心电图样式
heartbeatContainer: {
  height: 60,
  marginHorizontal: 20,
  marginBottom: 20,
  justifyContent: 'center',
  position: 'relative',
  overflow: 'hidden',
},

// 时间显示样式
timerText: {
  fontSize: 16,
  fontWeight: 'bold',
  color: '#333',
  backgroundColor: 'rgba(255, 255, 255, 0.8)',
  paddingHorizontal: 12,
  paddingVertical: 6,
  borderRadius: 15,
}
```

## 📱 用户体验

### 专注流程
1. **进入页面** - 显示专注模式界面
2. **点击播放** - 开始专注模式
3. **自动切换** - 图片每10秒自动切换
4. **心电图** - 显示动态心电图特效
5. **计时显示** - 实时显示专注时间
6. **手动控制** - 可随时暂停或切换图片

### 视觉反馈
- ✅ 播放/暂停图标切换
- ✅ 心电图动画启动/停止
- ✅ 时间计时器更新
- ✅ 图片平滑切换动画

## 🔄 状态管理

### 关键状态
```javascript
const [isPlaying, setIsPlaying] = useState(false);     // 播放状态
const [currentIndex, setCurrentIndex] = useState(1);   // 当前图片索引
const [focusTime, setFocusTime] = useState(0);         // 专注时间（秒）
```

### 定时器管理
```javascript
const autoSwitchTimerRef = useRef(null);  // 自动切换定时器
const focusTimerRef = useRef(null);       // 专注时间计时器
```

## 🎯 完美实现需求

✅ **图片10秒自动切换** - 播放时每10秒自动切换  
✅ **三个点手动切换** - 底部圆点指示器支持点击  
✅ **心电图特效** - 图片下方动态心电图动画  
✅ **播放键控制** - 点击开始，再点击停止  
✅ **专注计时** - 实时显示专注时间  

现在 MusicFocus.jsx 已经是一个完整的专注模式应用！🎉
