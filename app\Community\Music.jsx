import React, { useState } from 'react';
import { StyleSheet, View, TouchableOpacity, Image, ImageBackground } from 'react-native';
import { useRouter } from 'expo-router';
import ThemedView from '../../components/ThemedView';
import ThemedText from '../../components/ThemedText';
import LiquidGlassButton from '../../components/LiquidGlassButton';
import { Colors } from '../../constants/Colors';

const Music = () => {
  const router = useRouter();
  const [selectedMode, setSelectedMode] = useState(null);

  // 处理模式选择
  const handleModeSelect = (mode) => {
    setSelectedMode(mode);
    console.log('选择音乐模式:', mode);

    // 根据不同模式跳转到不同页面
    switch (mode) {
      case 'focus':
        router.push('/Community/MusicFocus');
        break;
      case 'relax':
        // 可以添加其他页面跳转
        console.log('跳转到放松模式页面');
        break;
      case 'study':
        // 可以添加其他页面跳转
        console.log('跳转到学习模式页面');
        break;
      case 'sleep':
        // 可以添加其他页面跳转
        console.log('跳转到睡眠模式页面');
        break;
      case 'nature':
        // 可以添加其他页面跳转
        console.log('跳转到自然音效页面');
        break;
      default:
        console.log('未知模式');
    }
  };

  return (
    <ThemedView style={styles.container}>
      {/* 安全视图 */}
      <View style={styles.safeArea} />

      {/* 背景图片容器 */}
      <ImageBackground
        source={require('../../assets/Community_image/Music.jpg')}
        style={styles.backgroundImage}
        resizeMode="cover"
      >
        {/* 顶部标题栏 - 融入文档流 */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.headerIcon}
            onPress={() => router.back()}
            accessible={true}
            accessibilityRole="button"
            accessibilityLabel="返回"
          >
            <Image
              source={require('../../assets/Arrows_left.png')}
              style={{ width: 24, height: 24, resizeMode: 'contain' }}
            />
          </TouchableOpacity>
          <View style={styles.headerTitleContainer}>
            <Image
              source={require('../../assets/Community_image/Music.png')}
              style={styles.headerTitleIcon}
            />
            <ThemedText style={styles.headerTitle}>音乐相伴</ThemedText>
          </View>
          <View style={styles.headerIcon} />
        </View>

        {/* 内容区域 */}
        <View style={styles.contentContainer}>
          {/* 音乐模式卡片区域 */}
          <View style={styles.cardsContainer}>

            {/* 专注模式 - 大卡片 */}
            <LiquidGlassButton
              style={[
                styles.largeCard,
                selectedMode === 'focus' && styles.selectedCard
              ]}
              onPress={() => handleModeSelect('focus')}
            >
              <View style={styles.cardContentWithIcon}>
                <Image
                  source={require('../../assets/Community_image/Book.png')}
                  style={styles.cardIcon}
                />
                <View style={styles.cardTextContent}>
                  <ThemedText style={styles.cardTitle}>专注模式</ThemedText>
                  <ThemedText style={styles.cardDescription}>
                    屏蔽杂念的沉浸结界，快速进入心流状态
                  </ThemedText>
                </View>
              </View>
            </LiquidGlassButton>

            {/* 第二行：休息模式和记忆模式 */}
            <View style={styles.smallCardsRow}>
              <LiquidGlassButton
                style={[
                  styles.smallCard,
                  selectedMode === 'rest' && styles.selectedCard
                ]}
                onPress={() => handleModeSelect('rest')}
              >
                <View style={styles.smallCardContentWithIcon}>
                  <Image
                    source={require('../../assets/Community_image/Rest.png')}
                    style={styles.smallCardIcon}
                  />
                  <View style={styles.smallCardTextContent}>
                    <ThemedText style={styles.smallCardTitle}>休息模式</ThemedText>
                    <ThemedText style={styles.smallCardDescription}>清空大脑缓存区</ThemedText>
                  </View>
                </View>
              </LiquidGlassButton>

              <LiquidGlassButton
                style={[
                  styles.smallCard,
                  selectedMode === 'memory' && styles.selectedCard
                ]}
                onPress={() => handleModeSelect('memory')}
              >
                <View style={styles.smallCardContentWithIcon}>
                  <Image
                    source={require('../../assets/Community_image/Memory.png')}
                    style={styles.smallCardIcon}
                  />
                  <View style={styles.smallCardTextContent}>
                    <ThemedText style={styles.smallCardTitle}>记忆模式</ThemedText>
                    <ThemedText style={styles.smallCardDescription}>给大脑装上记忆齿轮</ThemedText>
                  </View>
                </View>
              </LiquidGlassButton>
            </View>

            {/* 第三行：白噪音和创意模式 */}
            <View style={styles.smallCardsRow}>
              <LiquidGlassButton
                style={[
                  styles.smallCard,
                  selectedMode === 'whitenoise' && styles.selectedCard
                ]}
                onPress={() => handleModeSelect('whitenoise')}
              >
                <View style={styles.smallCardContentWithIcon}>
                  <Image
                    source={require('../../assets/Community_image/WhiteNoise.png')}
                    style={styles.smallCardIcon}
                  />
                  <View style={styles.smallCardTextContent}>
                    <ThemedText style={styles.smallCardTitle}>白噪音</ThemedText>
                    <ThemedText style={styles.smallCardDescription}>大脑天然的ASMR</ThemedText>
                  </View>
                </View>
              </LiquidGlassButton>

              <LiquidGlassButton
                style={[
                  styles.smallCard,
                  selectedMode === 'creative' && styles.selectedCard
                ]}
                onPress={() => handleModeSelect('creative')}
              >
                <View style={styles.smallCardContentWithIcon}>
                  <Image
                    source={require('../../assets/Community_image/Creativity.png')}
                    style={styles.smallCardIcon}
                  />
                  <View style={styles.smallCardTextContent}>
                    <ThemedText style={styles.smallCardTitle}>创意模式</ThemedText>
                    <ThemedText style={styles.smallCardDescription}>唤醒灵感的背景画布</ThemedText>
                  </View>
                </View>
              </LiquidGlassButton>
            </View>

          </View>
        </View>
      </ImageBackground>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF8F3'
  },
  safeArea: {
    height: 50,
    backgroundColor: '#FFF8F3',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingTop: 20,
  },
  headerIcon: {
    padding: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitleIcon: {
    width: 24,
    height: 24,
    marginRight: 8,
    resizeMode: 'contain',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  contentContainer: {
    flex: 1,
    paddingTop: 20, // 减少顶部间距
    paddingHorizontal: 16,
    justifyContent: 'center',
  },
  cardsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  // 大卡片样式（专注模式）
  largeCard: {
    width: '100%',
    height: 120,
    borderRadius: 20,
    marginBottom: 20,
    overflow: 'hidden',
  },
  cardContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  cardContentWithIcon: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  cardTextContent: {
    flex: 1,
    marginLeft: 16,
  },
  cardIcon: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
  },
  cardTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
    textAlign: 'left',
  },
  cardDescription: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 22,
    textAlign: 'left',
  },
  // 小卡片行样式
  smallCardsRow: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    marginBottom: 16,
    gap: 12,
  },
  // 小卡片样式
  smallCard: {
    flex: 1,
    height: 100,
    borderRadius: 20,
    overflow: 'hidden',
  },
  smallCardContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 12,
  },
  smallCardContentWithIcon: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
  },
  smallCardTextContent: {
    flex: 1,
    marginLeft: 8,
  },
  smallCardTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  smallCardIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  smallCardTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'left',
    marginBottom: 4,
  },
  smallCardDescription: {
    fontSize: 11,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'left',
    lineHeight: 14,
  },
  // 选中状态样式
  selectedCard: {
    backgroundColor: 'rgba(122, 60, 16, 0.9)',
    borderWidth: 2,
    borderColor: '#7A3C10',
  },
});

export default Music;