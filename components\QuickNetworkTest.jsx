// components/QuickNetworkTest.jsx
import React, { useState } from 'react';
import { View, TouchableOpacity, Platform } from 'react-native';
import ThemedText from './ThemedText';
import { getApiBaseUrl } from '../lib/apiConfig';
import { fetchWithAuth } from '../lib/fetchWithAuth';

const QuickNetworkTest = () => {
  const [testing, setTesting] = useState(false);
  const [result, setResult] = useState('');
  const [apiUrl, setApiUrl] = useState('');

  // 获取API URL
  React.useEffect(() => {
    const loadApiUrl = async () => {
      const url = await getApiBaseUrl();
      setApiUrl(url);
    };
    loadApiUrl();
  }, []);

  const testConnection = async () => {
    if (testing) return;

    setTesting(true);
    setResult('测试中...');

    try {
      const currentApiUrl = await getApiBaseUrl();
      console.log(`[${Platform.OS}] 快速网络测试开始`);
      console.log(`[${Platform.OS}] API_BASE_URL:`, currentApiUrl);

      // 测试基础连接
      const startTime = Date.now();
      const response = await fetchWithAuth(`${currentApiUrl}/index?userId=1`);
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      if (response.ok) {
        const data = await response.json();
        setResult(`✅ 连接成功 (${duration}ms)\n状态: ${response.status}\n数据: ${JSON.stringify(data).substring(0, 100)}...`);
        console.log(`[${Platform.OS}] 网络测试成功:`, { duration, status: response.status });
      } else {
        setResult(`❌ 连接失败\n状态: ${response.status}\n错误: ${response.statusText}`);
        console.log(`[${Platform.OS}] 网络测试失败:`, { status: response.status, statusText: response.statusText });
      }
      
    } catch (error) {
      setResult(`❌ 连接错误\n错误: ${error.message}`);
      console.error(`[${Platform.OS}] 网络测试错误:`, error);
    } finally {
      setTesting(false);
    }
  };

  return (
    <View style={{
      backgroundColor: '#f0f0f0',
      padding: 15,
      margin: 10,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: '#ddd'
    }}>
      <ThemedText style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10 }}>
        网络连接测试
      </ThemedText>
      
      <ThemedText style={{ fontSize: 12, color: '#666', marginBottom: 10 }}>
        API: {apiUrl}
      </ThemedText>
      
      <TouchableOpacity
        onPress={testConnection}
        disabled={testing}
        style={{
          backgroundColor: testing ? '#ccc' : '#007AFF',
          padding: 10,
          borderRadius: 5,
          marginBottom: 10
        }}
      >
        <ThemedText style={{ color: 'white', textAlign: 'center' }}>
          {testing ? '测试中...' : '测试连接'}
        </ThemedText>
      </TouchableOpacity>
      
      {result ? (
        <View style={{
          backgroundColor: 'white',
          padding: 10,
          borderRadius: 5,
          borderWidth: 1,
          borderColor: '#ddd'
        }}>
          <ThemedText style={{ fontSize: 12, fontFamily: 'monospace' }}>
            {result}
          </ThemedText>
        </View>
      ) : null}
    </View>
  );
};

export default QuickNetworkTest;
