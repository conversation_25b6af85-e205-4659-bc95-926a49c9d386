// components/RecommendationTest.jsx - 推荐功能测试组件
import React, { useState } from 'react';
import { View, TouchableOpacity, ScrollView, Platform } from 'react-native';
import ThemedText from './ThemedText';
import apiServices from '../lib/apiServices';

const RecommendationTest = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);

  // 测试新的推荐API
  const testRecommendations = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log(`[${Platform.OS}] 开始测试推荐API`);
      
      const data = await apiServices.content.getRecommendations();
      
      console.log(`[${Platform.OS}] 推荐API测试成功:`, data);
      setResult(data);
      
    } catch (err) {
      console.error(`[${Platform.OS}] 推荐API测试失败:`, err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // 测试推荐视频列表
  const testRecommendVideoList = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log(`[${Platform.OS}] 开始测试推荐视频列表`);
      
      const data = await apiServices.content.getRecommendVideoList(1);
      
      console.log(`[${Platform.OS}] 推荐视频列表测试成功:`, data);
      setResult(data);
      
    } catch (err) {
      console.error(`[${Platform.OS}] 推荐视频列表测试失败:`, err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // 测试首页数据
  const testIndexData = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log(`[${Platform.OS}] 开始测试首页数据`);
      
      const data = await apiServices.content.getIndexData(1);
      
      console.log(`[${Platform.OS}] 首页数据测试成功:`, data);
      setResult(data);
      
    } catch (err) {
      console.error(`[${Platform.OS}] 首页数据测试失败:`, err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const renderDataPreview = (data) => {
    if (!data) return null;

    const preview = JSON.stringify(data, null, 2);
    const truncated = preview.length > 500 ? preview.substring(0, 500) + '...' : preview;

    return (
      <View style={{
        backgroundColor: '#f5f5f5',
        padding: 10,
        borderRadius: 5,
        marginTop: 10
      }}>
        <ThemedText style={{ fontSize: 12, fontFamily: 'monospace' }}>
          {truncated}
        </ThemedText>
      </View>
    );
  };

  return (
    <ScrollView style={{ padding: 20 }}>
      <ThemedText style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 20 }}>
        推荐功能测试
      </ThemedText>

      {/* 测试按钮 */}
      <TouchableOpacity
        onPress={testRecommendations}
        disabled={loading}
        style={[buttonStyle, { backgroundColor: loading ? '#ccc' : '#007AFF' }]}
      >
        <ThemedText style={buttonTextStyle}>
          测试推荐API (getRecommendations)
        </ThemedText>
      </TouchableOpacity>

      <TouchableOpacity
        onPress={testRecommendVideoList}
        disabled={loading}
        style={[buttonStyle, { backgroundColor: loading ? '#ccc' : '#34C759' }]}
      >
        <ThemedText style={buttonTextStyle}>
          测试推荐视频列表 (getRecommendVideoList)
        </ThemedText>
      </TouchableOpacity>

      <TouchableOpacity
        onPress={testIndexData}
        disabled={loading}
        style={[buttonStyle, { backgroundColor: loading ? '#ccc' : '#FF9500' }]}
      >
        <ThemedText style={buttonTextStyle}>
          测试首页数据 (getIndexData)
        </ThemedText>
      </TouchableOpacity>

      {/* 状态显示 */}
      {loading && (
        <View style={{ marginTop: 20, alignItems: 'center' }}>
          <ThemedText style={{ color: '#666' }}>测试中...</ThemedText>
        </View>
      )}

      {error && (
        <View style={{
          backgroundColor: '#ffebee',
          padding: 15,
          borderRadius: 5,
          marginTop: 20,
          borderLeftWidth: 4,
          borderLeftColor: '#f44336'
        }}>
          <ThemedText style={{ color: '#d32f2f', fontWeight: 'bold' }}>
            错误:
          </ThemedText>
          <ThemedText style={{ color: '#d32f2f', marginTop: 5 }}>
            {error}
          </ThemedText>
        </View>
      )}

      {result && (
        <View style={{
          backgroundColor: '#e8f5e8',
          padding: 15,
          borderRadius: 5,
          marginTop: 20,
          borderLeftWidth: 4,
          borderLeftColor: '#4caf50'
        }}>
          <ThemedText style={{ color: '#2e7d32', fontWeight: 'bold' }}>
            测试成功:
          </ThemedText>
          
          {/* 数据统计 */}
          {result.data && (
            <View style={{ marginTop: 10 }}>
              <ThemedText style={{ color: '#2e7d32', fontSize: 12 }}>
                数据类型: {Array.isArray(result.data) ? '数组' : '对象'}
              </ThemedText>
              {Array.isArray(result.data) && (
                <ThemedText style={{ color: '#2e7d32', fontSize: 12 }}>
                  数组长度: {result.data.length}
                </ThemedText>
              )}
              {result.data.interestRecommend && (
                <ThemedText style={{ color: '#2e7d32', fontSize: 12 }}>
                  兴趣推荐: {result.data.interestRecommend.length} 项
                </ThemedText>
              )}
              {result.data.profileRecommend && (
                <ThemedText style={{ color: '#2e7d32', fontSize: 12 }}>
                  画像推荐: {result.data.profileRecommend.length} 项
                </ThemedText>
              )}
              {result.data.featuredContentList && (
                <ThemedText style={{ color: '#2e7d32', fontSize: 12 }}>
                  精品内容: {result.data.featuredContentList.length} 项
                </ThemedText>
              )}
            </View>
          )}
          
          {renderDataPreview(result)}
        </View>
      )}
    </ScrollView>
  );
};

const buttonStyle = {
  padding: 15,
  borderRadius: 8,
  marginBottom: 10,
  alignItems: 'center'
};

const buttonTextStyle = {
  color: 'white',
  fontWeight: 'bold',
  fontSize: 14
};

export default RecommendationTest;
