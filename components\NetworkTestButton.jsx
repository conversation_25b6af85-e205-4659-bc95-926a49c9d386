// components/NetworkTestButton.jsx
import React, { useState } from 'react';
import { View, TouchableOpacity, Alert, Platform } from 'react-native';
import ThemedText from './ThemedText';
import { testNetworkConnection, getIPAddressInstructions, troubleshootNetwork } from '../lib/networkTest';
import { checkBackendStatus, quickPing, diagnoseNetwork } from '../lib/backendCheck';
import { getApiBaseUrl, getDevIP } from '../lib/apiConfig';

const NetworkTestButton = ({ style }) => {
  const [testing, setTesting] = useState(false);

  const runNetworkTest = async () => {
    if (testing) return;

    setTesting(true);

    try {
      console.log('开始综合网络诊断...');

      // 显示当前配置信息
      const apiBaseUrl = await getApiBaseUrl();
      const configInfo = `
平台: ${Platform.OS}
API基础URL: ${apiBaseUrl}
${Platform.OS !== 'web' ? `开发机器IP: ${getDevIP()}` : '使用localhost (Web端)'}
      `;

      console.log('当前配置:', configInfo);

      // 运行综合诊断
      const diagnosis = await diagnoseNetwork();

      // 构建结果消息
      let message = `网络诊断完成\n\n`;

      // Ping结果
      if (diagnosis.ping.success) {
        message += `✅ 基础连接: 成功 (${diagnosis.ping.duration}ms)\n`;
      } else {
        message += `❌ 基础连接: 失败 (${diagnosis.ping.error})\n`;
      }

      // 后端检查结果
      const { summary } = diagnosis.backend;
      message += `📊 API检查: ${summary.success}/${summary.total} 成功 (${summary.successRate}%)\n\n`;

      // 建议
      message += '建议:\n';
      diagnosis.recommendations.forEach((rec, index) => {
        message += `${index + 1}. ${rec}\n`;
      });

      // 详细失败信息
      const failedChecks = diagnosis.backend.results.filter(r => !r.success);
      if (failedChecks.length > 0) {
        message += '\n失败的检查:\n';
        failedChecks.forEach(check => {
          message += `• ${check.name}: ${check.error}\n`;
        });
      }

      Alert.alert('网络诊断结果', message);

    } catch (error) {
      console.error('网络诊断失败:', error);
      Alert.alert(
        '网络诊断失败',
        `诊断过程中出现错误:\n${error.message}\n\n请检查网络配置。`
      );
    } finally {
      setTesting(false);
    }
  };

  const showNetworkHelp = async () => {
    const apiBaseUrl = await getApiBaseUrl();
    const helpMessage = `
网络配置帮助

当前配置:
• 平台: ${Platform.OS}
• API URL: ${apiBaseUrl}
${Platform.OS !== 'web' ? `• 开发机器IP: ${getDevIP()}` : '• 使用localhost (Web端)'}

如果手机端无法连接:
1. 确认后端服务正在运行
2. 获取开发机器的实际IP地址
3. 更新 constants/index.js 中的IP
4. 确保手机和电脑在同一WiFi
5. 检查防火墙设置

获取IP地址方法:
• Windows: 运行 ipconfig
• Mac: 运行 ifconfig
• Linux: 运行 ip addr show
    `;
    
    Alert.alert('网络配置帮助', helpMessage);
    
    // 同时在控制台输出详细说明
    getIPAddressInstructions();
    troubleshootNetwork();
  };

  return (
    <View style={[{ flexDirection: 'row', gap: 10 }, style]}>
      <TouchableOpacity
        onPress={runNetworkTest}
        disabled={testing}
        style={{
          backgroundColor: testing ? '#ccc' : '#007AFF',
          paddingHorizontal: 15,
          paddingVertical: 8,
          borderRadius: 8,
          flex: 1
        }}
      >
        <ThemedText style={{ 
          color: 'white', 
          textAlign: 'center',
          fontSize: 14
        }}>
          {testing ? '测试中...' : '测试网络连接'}
        </ThemedText>
      </TouchableOpacity>
      
      <TouchableOpacity
        onPress={showNetworkHelp}
        style={{
          backgroundColor: '#34C759',
          paddingHorizontal: 15,
          paddingVertical: 8,
          borderRadius: 8,
          flex: 1
        }}
      >
        <ThemedText style={{ 
          color: 'white', 
          textAlign: 'center',
          fontSize: 14
        }}>
          网络帮助
        </ThemedText>
      </TouchableOpacity>
    </View>
  );
};

export default NetworkTestButton;
