import React, { useState } from 'react';
import { View, Image, ScrollView, TouchableOpacity, useColorScheme, StyleSheet } from 'react-native';
import ThemedText from '../components/ThemedText';
import ThemedView from '../components/ThemedView';
import { Colors } from '../constants/Colors';
import { useRouter } from 'expo-router';

const tabs = ['语言陪伴', '全国'];

export default function CommunityFriends() {
  const colorScheme = useColorScheme();
  const theme = Colors[colorScheme] ?? Colors.light;
  const [activeTab, setActiveTab] = useState('语言陪伴');
  const router = useRouter();

  return (

    <ThemedView style={{ flex: 1, backgroundColor: '#FFF8F3' }}>
      {/* 顶部返回和菜单栏 */}
      <View style={styles.fixedHeader}>
        <TouchableOpacity
          onPress={() => router.push('/(dashboard)/community?tab=community&filterTag=互助站')}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="返回社区页面"
        >
          <Image source={require('../assets/FrameTwo.png')} style={{ width: 28, height: 28, marginRight: 8 }} />
        </TouchableOpacity>
        <View style={{ flex: 1 }} />
        <TouchableOpacity>
          <ThemedText style={{ fontSize: 24, color: theme.iconColor }}>⋯</ThemedText>
        </TouchableOpacity>
      </View>
      {/* 标题整体下移 */}
      <View style={{ marginTop: 18, paddingHorizontal: 16 }}>
        <ThemedText style={{ fontSize: 22, fontWeight: 'bold', color: theme.title }}>有趣数学</ThemedText>
      </View>
      {/* 统计与描述 */}
      <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 4, marginBottom: 2, paddingHorizontal: 16 }}>
        <ThemedText style={{ fontSize: 16, color: theme.iconColor }}>🌐 全国6087篇帖子</ThemedText>
      </View>
      <ThemedText style={{ color: theme.text, fontSize: 14, marginBottom: 10, paddingHorizontal: 16 }}>
        深夜的数学诱惑,让人忍不住想吃上一口！
      </ThemedText>
      {/* 帖子卡片 */}
      <ScrollView style={{ flex: 1 }} contentContainerStyle={{ padding: 0, paddingBottom: 80 }}>
        <View style={{ backgroundColor: theme.uiBackground, borderRadius: 18, marginHorizontal: 12, marginBottom: 18, padding: 18, shadowColor: '#000', shadowOpacity: 0.04, shadowRadius: 8 }}>
          {/* 卡片内分页器Tab */}
          <View style={{ flexDirection: 'row', justifyContent: 'center', marginBottom: 16 }}>
            {tabs.map(tab => (
              <TouchableOpacity
                key={tab}
                onPress={() => setActiveTab(tab)}
                style={{
                  flex: 1,
                  alignItems: 'center',
                  paddingVertical: 8,
                  borderBottomWidth: 3,
                  borderColor: activeTab === tab ? theme.iconColorFocused : 'transparent',
                  marginHorizontal: 8
                }}
              >
                <ThemedText style={{ fontSize: 18, fontWeight: 'bold', color: activeTab === tab ? theme.iconColorFocused : theme.iconColor }}>{tab}</ThemedText>
              </TouchableOpacity>
            ))}
          </View>
          {/* 头像、昵称、等级、菜单 */}
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
            <Image source={require('../assets/AIBrain.png')} style={{ width: 54, height: 54, borderRadius: 27, marginRight: 12 }} />
            <View style={{ flex: 1 }}>
              <ThemedText style={{ fontWeight: 'bold', fontSize: 16, color: theme.title }}>Emo Jacylove</ThemedText>
              <ThemedText style={{ color: theme.iconColor, fontSize: 13, marginTop: 2 }}>Lv.30</ThemedText>
            </View>
            <TouchableOpacity>
              <ThemedText style={{ fontSize: 22, color: theme.iconColor }}>⋯</ThemedText>
            </TouchableOpacity>
          </View>
          {/* 帖子内容 */}
          <ThemedText style={{ fontSize: 15, color: theme.text, marginBottom: 8 }}>
            师从陶哲轩的券商巨头打造“数学超级智能”，要用AI解决千禧年难题估值已达近9亿美元
          </ThemedText>
          {/* 正方形图片占位 */}
          <View style={{ width: '100%', aspectRatio: 1, backgroundColor: '#e0e0e0', borderRadius: 18, marginBottom: 10, overflow: 'hidden', alignItems: 'center', justifyContent: 'center' }}>
            <Image source={require('../assets/AIBrain.png')} style={{ width: '100%', height: '100%', resizeMode: 'cover' }} />
          </View>
          {/* 标签区 */}
          <View style={{ flexDirection: 'row', marginBottom: 10 }}>
            <View style={{ backgroundColor: '#f2f2f2', borderRadius: 12, paddingHorizontal: 12, paddingVertical: 4, marginRight: 8 }}>
              <ThemedText style={{ color: theme.iconColor, fontSize: 13 }}>诱人数学</ThemedText>
            </View>
            <View style={{ backgroundColor: '#f2f2f2', borderRadius: 12, paddingHorizontal: 12, paddingVertical: 4, marginRight: 8 }}>
              <ThemedText style={{ color: theme.iconColor, fontSize: 13 }}>数学智能</ThemedText>
            </View>
            <View style={{ backgroundColor: '#f2f2f2', borderRadius: 12, paddingHorizontal: 12, paddingVertical: 4 }}>
              <ThemedText style={{ color: theme.iconColor, fontSize: 13 }}>千禧问题</ThemedText>
            </View>
          </View>
          {/* 操作区 */}
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 6 }}>
            <TouchableOpacity>
              <Image source={require('../assets/Profile_image/Like.png')} style={{ width: 22, height: 22, marginRight: 18 }} />
            </TouchableOpacity>
            <TouchableOpacity>
              <Image source={require('../assets/Talking.png')} style={{ width: 22, height: 22, marginRight: 18 }} />
            </TouchableOpacity>
            <View style={{ flex: 1 }} />
            <TouchableOpacity>
              <Image source={require('../assets/Translate.png')} style={{ width: 22, height: 22, marginRight: 18 }} />
            </TouchableOpacity>
            <TouchableOpacity>
              <Image source={require('../assets/Repost.png')} style={{ width: 22, height: 22 }} />
            </TouchableOpacity>
          </View>
          {/* 评论区 */}
          <ThemedText style={{ color: theme.iconColor, fontSize: 13, marginBottom: 10 }}>Faker:哇,我也要去学习用AI处理数学问题</ThemedText>
        </View>
      </ScrollView>
      {/* 固定底部关注和发布按钮 */}
      <View style={{ position: 'absolute', left: 0, right: 0, bottom: 0, flexDirection: 'row', backgroundColor: '#FFF8F3', padding: 12 }}>
        <TouchableOpacity style={{ flex: 1, backgroundColor: '#ededed', borderRadius: 16, alignItems: 'center', justifyContent: 'center', height: 50, marginRight: 8 }}>
          <ThemedText style={{ fontSize: 17, fontWeight: 'bold', color: theme.title }}>关注</ThemedText>
        </TouchableOpacity>
        <TouchableOpacity style={{ flex: 1, backgroundColor: '#ededed', borderRadius: 16, alignItems: 'center', justifyContent: 'center', height: 50 }}>
          <ThemedText style={{ fontSize: 17, fontWeight: 'bold', color: theme.title }}>发布</ThemedText>
        </TouchableOpacity>
      </View>
    </ThemedView>

  );
}

const styles = StyleSheet.create({
  fixedHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingHorizontal: 16,
    paddingBottom: 16,
    backgroundColor: 'rgba(255, 248, 243, 0.95)',
    backdropFilter: 'blur(10px)',
    borderBottomWidth: 0.5,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
});