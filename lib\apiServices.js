// lib/apiServices.js - 统一的API服务层
import apiClient from './apiClient';
import { Platform } from 'react-native';

/**
 * 认证相关API
 */
export const authApi = {
  /**
   * 用户登录
   */
  async login(userAccount, userPassword) {
    console.log(`[${Platform.OS}] 开始登录:`, { userAccount });

    const response = await apiClient.authRequest('/login', {
      method: 'POST',
      body: { userAccount, userPassword }
    });

    if (!response.ok) {
      throw new Error(response.data || '登录失败');
    }

    console.log(`[${Platform.OS}] 登录API原始响应:`, response.data);

    // 处理标准的API返回结构：{ code: 0, message: "ok", data: {...} }
    let userData = null;
    let token = null;

    if (response.data.code === 0 && response.data.data) {
      // 标准结构
      userData = response.data.data;
      token = response.data.data.token || response.data.token;
      console.log(`[${Platform.OS}] 解析标准结构登录数据:`, userData);
    } else if (response.data.data) {
      // 兼容直接返回 data 的情况
      userData = response.data.data;
      token = response.data.data.token || response.data.token;
    } else if (response.data.userAccount || response.data.id) {
      // 兼容直接返回用户数据的情况
      userData = response.data;
      token = response.data.token;
    } else {
      throw new Error('登录响应数据格式错误');
    }

    // 增强token提取逻辑 - 尝试从多个可能的位置获取token
    if (!token) {
      // 尝试从响应的各个层级查找token
      token = response.data.token ||
        response.data.data?.token ||
        response.data.accessToken ||
        response.data.data?.accessToken ||
        response.token ||
        userData?.token ||
        userData?.accessToken;

      console.log(`[${Platform.OS}] 增强token提取，找到token:`, !!token);
    }

    // 保存token
    if (token) {
      await apiClient.saveAuthToken(token);
      console.log(`[${Platform.OS}] Token已保存:`, token.substring(0, 20) + '...');
    } else {
      console.warn(`[${Platform.OS}] 登录响应中未找到token，完整响应:`, JSON.stringify(response.data, null, 2));
    }

    console.log(`[${Platform.OS}] 登录成功，返回用户数据:`, userData);

    // 返回完整的响应结构，保持兼容性
    return {
      code: response.data.code || 0,
      message: response.data.message || 'ok',
      data: userData
    };
  },

  /**
   * 用户注册
   */
  async register(userAccount, userPassword, checkPassword) {
    console.log(`[${Platform.OS}] 开始注册:`, { userAccount });

    const response = await apiClient.authRequest('/register', {
      method: 'POST',
      body: { userAccount, userPassword, checkPassword }
    });

    if (!response.ok) {
      throw new Error(response.data || '注册失败');
    }

    console.log(`[${Platform.OS}] 注册成功`);
    return response.data;
  },

  /**
   * 忘记密码
   */
  async forgotPassword(userAccount) {
    console.log(`[${Platform.OS}] 忘记密码:`, { userAccount });

    const response = await apiClient.authRequest('/forgot-password', {
      method: 'POST',
      body: { userAccount }
    });

    if (!response.ok) {
      throw new Error(response.data || '操作失败');
    }

    return response.data;
  },

  /**
   * 轻量级用户验证
   */
  async verifyUser() {
    console.log(`[${Platform.OS}] 开始轻量级用户验证`);

    const response = await apiClient.get('/user/verify');

    if (!response.ok) {
      throw new Error('用户验证失败');
    }

    console.log(`[${Platform.OS}] 用户验证成功`);
    return response.data;
  },

  /**
   * 登出
   */
  async logout() {
    console.log(`[${Platform.OS}] 用户登出`);
    await apiClient.removeAuthToken();
    return true;
  },

  /**
   * 获取当前用户信息
   */
  async getCurrentUser() {
    console.log(`[${Platform.OS}] 获取当前用户信息`);

    const response = await apiClient.authRequest('/me', {
      method: 'GET',
      requireAuth: true
    });

    if (!response.ok) {
      throw new Error('获取用户信息失败');
    }

    // 处理不同的响应格式
    let userData = null;
    const data = response.data;

    if (data.data && data.data.id && data.data.userAccount) {
      userData = data.data;
    } else if (data.id && data.userAccount) {
      userData = data;
    } else if (data.user && data.user.id && data.user.userAccount) {
      userData = data.user;
    }

    if (userData) {
      console.log(`[${Platform.OS}] 用户信息获取成功:`, userData);
      return userData;
    } else {
      throw new Error('用户数据格式无效');
    }
  },

  /**
   * 轻量级用户验证（只获取 id 和 userAccount）
   */
  async validateUserBasic() {
    console.log(`[${Platform.OS}] 开始轻量级用户验证`);

    try {
      const response = await apiClient.authRequest('/me', {
        method: 'GET',
        requireAuth: true
      });

      if (!response.ok) {
        return null;
      }

      const data = response.data;
      let basicUserData = null;

      // 只提取 id 和 userAccount，忽略其他字段以减少处理
      if (data.data && (data.data.id || data.data.userAccount)) {
        basicUserData = {
          id: data.data.id,
          userAccount: data.data.userAccount
        };
      } else if (data.id || data.userAccount) {
        basicUserData = {
          id: data.id,
          userAccount: data.userAccount
        };
      } else if (data.user && (data.user.id || data.user.userAccount)) {
        basicUserData = {
          id: data.user.id,
          userAccount: data.user.userAccount
        };
      }

      if (basicUserData && (basicUserData.id || basicUserData.userAccount)) {
        console.log(`[${Platform.OS}] 轻量级用户验证成功:`, basicUserData);
        return basicUserData;
      } else {
        console.log(`[${Platform.OS}] 轻量级用户验证数据格式无效`);
        return null;
      }
    } catch (error) {
      console.error(`[${Platform.OS}] 轻量级用户验证错误:`, error);
      return null;
    }
  },

  /**
   * 验证JWT Token是否有效
   */
  async validateToken() {
    // 注释掉token验证逻辑，直接返回true保持登录状态
    return true;
  },

  /**
   * 检查用户是否已登录
   * @returns {Promise<{isLoggedIn: boolean, user: object|null, token: string|null}>}
   */
  async checkLoginStatus() {
    try {
      console.log(`[${Platform.OS}] 检查用户登录状态`);

      // 1. 检查是否有token
      const token = await apiClient.getAuthToken();
      console.log(`[${Platform.OS}] 获取到的token:`, token ? `${token.substring(0, 20)}...` : 'null');

      if (!token) {
        console.log(`[${Platform.OS}] 未找到认证token`);
        return { isLoggedIn: false, user: null, token: null };
      }

      // 2. 验证token是否有效
      try {
        const user = await this.validateUserBasic();
        if (user && (user.id || user.userAccount)) {
          console.log(`[${Platform.OS}] 用户已登录:`, user);
          return { isLoggedIn: true, user, token };
        } else {
          console.log(`[${Platform.OS}] Token无效或用户信息不完整`);
          return { isLoggedIn: false, user: null, token: null };
        }
      } catch (error) {
        console.log(`[${Platform.OS}] Token验证失败:`, error.message);
        return { isLoggedIn: false, user: null, token: null };
      }
    } catch (error) {
      console.error(`[${Platform.OS}] 检查登录状态失败:`, error);
      return { isLoggedIn: false, user: null, token: null };
    }
  },

  /**
   * 确保用户已登录，如果没有登录则抛出错误
   * @param {string} action - 需要登录的操作描述
   * @throws {Error} 如果用户未登录
   * @returns {Promise<object>} 用户信息
   */
  async requireLogin(action = '执行此操作') {
    const { isLoggedIn, user } = await this.checkLoginStatus();

    if (!isLoggedIn) {
      throw new Error(`请先登录后再${action}`);
    }

    return user;
  },

  /**
   * 安全执行需要登录的操作
   * @param {Function} operation - 需要执行的操作
   * @param {string} actionName - 操作名称
   * @param {Function} onLoginRequired - 需要登录时的回调
   * @returns {Promise<any>} 操作结果
   */
  async executeWithLogin(operation, actionName = '操作', onLoginRequired = null) {
    try {
      // 检查登录状态
      const user = await this.requireLogin(actionName);

      // 执行操作
      return await operation(user);
    } catch (error) {
      if (error.message.includes('登录')) {
        console.log(`[${Platform.OS}] ${actionName}需要登录:`, error.message);
        if (onLoginRequired) {
          onLoginRequired(error);
        }
        throw error;
      } else {
        console.error(`[${Platform.OS}] ${actionName}执行失败:`, error);
        throw error;
      }
    }
  },

  /**
   * 获取用户ID（如果已登录）
   * @returns {Promise<string|null>} 用户ID或null
   */
  async getUserId() {
    try {
      const { isLoggedIn, user } = await this.checkLoginStatus();
      return isLoggedIn ? user?.id : null;
    } catch (error) {
      console.error(`[${Platform.OS}] 获取用户ID失败:`, error);
      return null;
    }
  },

  /**
   * 根据用户ID获取用户信息
   * @param {string} userId - 用户ID
   * @returns {Promise<object>} 用户信息
   */
  async getUserById(userId) {
    console.log(`[${Platform.OS}] 获取用户信息:`, { userId });

    if (!userId) {
      throw new Error('用户ID不能为空');
    }

    const response = await apiClient.get(`/user/${userId}`, {
      requireAuth: true
    });

    if (!response.ok) {
      throw new Error('获取用户信息失败');
    }

    // 处理不同的响应格式
    let userData = null;
    const data = response.data;

    if (data.data && data.data.id) {
      userData = data.data;
    } else if (data.id) {
      userData = data;
    } else if (data.user && data.user.id) {
      userData = data.user;
    }

    if (userData) {
      console.log(`[${Platform.OS}] 用户信息获取成功:`, userData);
      return userData;
    } else {
      throw new Error('用户数据格式无效');
    }
  },

  /**
   * 批量获取用户信息
   * @param {string[]} userIds - 用户ID数组
   * @returns {Promise<object[]>} 用户信息数组
   */
  async getUsersByIds(userIds) {
    console.log(`[${Platform.OS}] 批量获取用户信息:`, { userIds, count: userIds.length });

    if (!userIds || userIds.length === 0) {
      return [];
    }

    const response = await apiClient.post('/user/batch', {
      userIds
    }, {
      requireAuth: true
    });

    if (!response.ok) {
      throw new Error('批量获取用户信息失败');
    }

    // 处理响应数据
    const data = response.data;
    let usersData = [];

    if (data.data && Array.isArray(data.data)) {
      usersData = data.data;
    } else if (Array.isArray(data)) {
      usersData = data;
    } else if (data.users && Array.isArray(data.users)) {
      usersData = data.users;
    }

    console.log(`[${Platform.OS}] 批量获取用户信息成功:`, { count: usersData.length });
    return usersData;
  },

  /**
   * 检查是否为游客模式
   * @returns {Promise<boolean>} 是否为游客模式
   */
  async isGuestMode() {
    const { isLoggedIn } = await this.checkLoginStatus();
    return !isLoggedIn;
  },

  /**
   * 测试网络连接
   */
  async testNetworkConnection() {
    console.log(`[${Platform.OS}] 测试网络连接`);

    try {
      return await apiClient.testConnection();
    } catch (error) {
      console.error(`[${Platform.OS}] 网络连接测试失败:`, error);
      return false;
    }
  },

  // ==================== 游客模式管理 ====================

  guestSessionKey: 'guest_session',
  guestDataKey: 'guest_data',

  /**
   * 创建游客会话
   */
  async createGuestSession() {
    const guestUser = {
      id: 'guest_' + Date.now(),
      userAccount: 'guest',
      userName: '游客用户',
      userAvatar: null,
      userProfile: null,
      userRole: 'guest',
      profileSetupCompleted: true,
      isGuest: true,
      sessionCreated: new Date().toISOString()
    };

    await apiClient.storage.setItem(this.guestSessionKey, JSON.stringify(guestUser));
    console.log(`[${Platform.OS}] 游客会话已创建:`, guestUser);

    return guestUser;
  },

  /**
   * 获取游客会话
   */
  async getGuestSession() {
    try {
      const sessionData = await apiClient.storage.getItem(this.guestSessionKey);
      if (sessionData) {
        const guestUser = JSON.parse(sessionData);
        console.log(`[${Platform.OS}] 恢复游客会话:`, guestUser);
        return guestUser;
      }
    } catch (error) {
      console.error(`[${Platform.OS}] 获取游客会话失败:`, error);
    }
    return null;
  },

  /**
   * 清除游客会话
   */
  async clearGuestSession() {
    try {
      await apiClient.storage.removeItem(this.guestSessionKey);
      await apiClient.storage.removeItem(this.guestDataKey);
      console.log(`[${Platform.OS}] 游客会话已清除`);
    } catch (error) {
      console.error(`[${Platform.OS}] 清除游客会话失败:`, error);
    }
  },

  /**
   * 检查游客会话是否过期
   */
  async isGuestSessionExpired(maxAgeHours = 24) {
    const session = await this.getGuestSession();
    if (!session || !session.sessionCreated) {
      return true;
    }

    const createdTime = new Date(session.sessionCreated);
    const now = new Date();
    const ageHours = (now - createdTime) / (1000 * 60 * 60);

    return ageHours > maxAgeHours;
  },

  /**
   * 预加载游客数据
   */
  async preloadGuestData() {
    try {
      console.log(`[${Platform.OS}] 开始预加载游客数据`);

      // 直接调用API，避免循环依赖
      const [indexResponse, recommendResponse] = await Promise.all([
        apiClient.get('/index', { transformImages: true, requireAuth: false }),
        apiClient.get('/index', { transformImages: true, requireAuth: false })
      ]);

      const guestData = {
        indexData: indexResponse?.data?.data || {},
        recommendations: recommendResponse?.data?.data || [],
        loadedAt: new Date().toISOString()
      };

      await apiClient.storage.setItem(this.guestDataKey, JSON.stringify(guestData));

      console.log(`[${Platform.OS}] 游客数据预加载完成:`, {
        indexDataKeys: Object.keys(guestData.indexData),
        recommendationsCount: Array.isArray(guestData.recommendations) ? guestData.recommendations.length : 0
      });

      return guestData;
    } catch (error) {
      console.error(`[${Platform.OS}] 预加载游客数据失败:`, error);
      return {
        indexData: {},
        recommendations: [],
        error: error.message
      };
    }
  },

  /**
   * 获取缓存的游客数据
   */
  async getCachedGuestData() {
    try {
      const cachedData = await apiClient.storage.getItem(this.guestDataKey);
      if (cachedData) {
        const data = JSON.parse(cachedData);
        console.log(`[${Platform.OS}] 使用缓存的游客数据`);
        return data;
      }
    } catch (error) {
      console.error(`[${Platform.OS}] 获取缓存游客数据失败:`, error);
    }
    return null;
  },

  /**
   * 完整的游客登录流程
   */
  async performGuestLogin(setUser, setRecommendations) {
    try {
      console.log(`[${Platform.OS}] 开始游客登录流程`);

      const guestUser = await this.createGuestSession();
      setUser(guestUser);

      const cachedData = await this.getCachedGuestData();
      if (cachedData && cachedData.recommendations) {
        setRecommendations(cachedData.recommendations);
        console.log(`[${Platform.OS}] 使用缓存推荐数据`);
      }

      this.preloadGuestData().then((freshData) => {
        if (freshData.recommendations && freshData.recommendations.length > 0) {
          setRecommendations(freshData.recommendations);
          console.log(`[${Platform.OS}] 更新为最新推荐数据`);
        }
      }).catch((error) => {
        console.warn(`[${Platform.OS}] 后台更新数据失败:`, error);
      });

      return {
        success: true,
        user: guestUser,
        message: '游客登录成功'
      };

    } catch (error) {
      console.error(`[${Platform.OS}] 游客登录流程失败:`, error);
      return {
        success: false,
        error: error.message,
        message: '游客登录失败'
      };
    }
  }
};

/**
 * 内容相关API
 */
export const contentApi = {
  /**
   * 获取首页数据（支持游客模式）
   */
  async getIndexData(userId = null) {
    console.log(`[${Platform.OS}] 获取首页数据:`, { userId, mode: userId ? '登录用户' : '游客模式' });

    // 构建请求URL，游客模式不传userId参数
    const endpoint = userId ? `/index?userId=${userId}` : '/index';

    const response = await apiClient.get(endpoint, {
      transformImages: true, // 自动转换图片URL
      requireAuth: !!userId // 游客模式不需要认证
    });

    if (!response.ok) {
      throw new Error('获取首页数据失败');
    }

    console.log(`[${Platform.OS}] 首页API原始返回:`, response.data);

    // 处理标准的API返回结构：{ code: 0, message: "ok", data: {...} }
    if (response.data.code === 0 && response.data.data) {
      console.log(`[${Platform.OS}] 解析首页数据:`, response.data.data);
      return { data: response.data.data }; // 返回标准格式
    } else if (response.data.data) {
      // 兼容直接返回 data 的情况
      return { data: response.data.data };
    } else {
      // 兼容直接返回内容的情况
      return { data: response.data };
    }
  },

  /**
   * 获取精品内容列表
   */
  async getFeaturedContentList() {
    const response = await this.getIndexData();
    const data = response.data || {};

    // 优先使用新的数据字段，保持对旧数据结构的兼容性
    const featuredData = data.featuredContentList || [];

    console.log(`[${Platform.OS}] 精品内容数据:`, featuredData.length);
    return { data: featuredData };
  },

  /**
   * 获取热门课程列表
   */
  async getHotCourseList() {
    const response = await this.getIndexData();
    const data = response.data || {};

    // 优先使用新的数据字段，保持对旧数据结构的兼容性
    const hotData = data.hotCourseList || [];

    console.log(`[${Platform.OS}] 热门课程数据:`, hotData.length);
    return { data: hotData };
  },

  /**
   * 获取最新课程列表
   */
  async getLatestCourseList() {
    const response = await this.getIndexData();
    const data = response.data || {};

    // 优先使用新的数据字段，保持对旧数据结构的兼容性
    const latestData = data.latestCourseList || [];

    console.log(`[${Platform.OS}] 最新课程数据:`, latestData.length);
    return { data: latestData };
  },

  /**
   * 获取推荐视频列表（支持游客模式）
   */
  async getRecommendVideoList(userId = null) {
    const response = await this.getIndexData(userId);
    const data = response.data || {};

    // 优先使用兴趣推荐，如果没有则使用画像推荐，最后兼容旧结构
    const interestRecommend = data.interestRecommend || [];
    const profileRecommend = data.profileRecommend || [];
    const oldRecommendData = data.recommendVideoList || [];

    // 游客模式优先使用通用推荐数据
    const guestRecommend = data.guestRecommend || data.defaultRecommend || [];

    let recommendData;
    if (!userId) {
      // 游客模式：优先使用游客推荐，然后是通用数据
      recommendData = guestRecommend.length > 0 ? guestRecommend :
        oldRecommendData.length > 0 ? oldRecommendData :
          interestRecommend.length > 0 ? interestRecommend :
            profileRecommend;
    } else {
      // 登录用户：优先使用个性化推荐
      recommendData = interestRecommend.length > 0 ? interestRecommend :
        profileRecommend.length > 0 ? profileRecommend :
          guestRecommend.length > 0 ? guestRecommend :
            oldRecommendData;
    }

    console.log(`[${Platform.OS}] 推荐数据解析:`, {
      mode: userId ? '登录用户' : '游客模式',
      interestRecommend: interestRecommend.length,
      profileRecommend: profileRecommend.length,
      guestRecommend: guestRecommend.length,
      oldRecommendData: oldRecommendData.length,
      finalData: recommendData.length
    });

    return { data: recommendData };
  },

  /**
   * 获取推荐内容（支持游客模式，自动检测是否有token）
   */
  async getRecommendations(userId = null) {
    console.log(`[${Platform.OS}] 开始获取推荐内容:`, { userId, mode: userId ? '登录用户' : '游客模式' });

    // 检查是否有token来决定是否需要认证
    const token = await apiClient.getAuthToken();
    const hasAuth = !!token;

    // 构建请求URL
    const endpoint = userId ? `/index?userId=${userId}` : '/index';

    const response = await apiClient.get(endpoint, {
      transformImages: true, // 自动转换图片URL
      requireAuth: hasAuth && !!userId // 有token且有userId时才需要认证
    });

    if (!response.ok) {
      throw new Error(`获取推荐失败：${response.data}`);
    }

    console.log(`[${Platform.OS}] API原始返回数据:`, response.data);

    // 处理标准的API返回结构：{ code: 0, message: "ok", data: {...} }
    if (response.data.code === 0 && response.data.data) {
      console.log(`[${Platform.OS}] 解析推荐数据:`, response.data.data);
      return response.data.data; // 返回 data 部分，包含 interestRecommend, profileRecommend 等
    } else if (response.data.data) {
      // 兼容直接返回 data 的情况
      return response.data.data;
    } else {
      // 兼容直接返回推荐内容的情况
      return response.data;
    }
  }
};

/**
 * 用户资料相关API
 */
export const profileApi = {
  /**
   * 保存用户标签并生成AI画像
   */
  async saveUserTagAndGenerateProfile(selections) {
    console.log(`[${Platform.OS}] 保存用户标签并生成AI画像:`, selections);

    // 检查是否有认证token
    const token = await apiClient.getAuthToken();
    if (!token) {
      console.error(`[${Platform.OS}] 保存标签失败: 用户未登录`);
      throw new Error('请先登录后再保存标签');
    }

    console.log(`[${Platform.OS}] 找到认证token，继续保存标签`);

    // 将selections对象转换为tagIdList数组
    const tagIdList = Object.values(selections).flat();

    const response = await apiClient.post('/profile/save', {
      tagIdList
    }, {
      requireAuth: true // 明确要求认证
    });

    if (!response.ok) {
      if (response.status === 401) {
        // Token可能已过期，清除token并提示重新登录
        await apiClient.removeAuthToken();
        throw new Error('登录已过期，请重新登录');
      }
      throw new Error(response.data?.message || '保存失败');
    }

    if (response.data?.code !== 0) {
      throw new Error(response.data?.message || '保存失败');
    }

    console.log(`[${Platform.OS}] AI画像生成成功`);
    return response.data.data;
  },

  /**
   * 获取用户资料
   */
  async getUserProfile() {
    console.log(`[${Platform.OS}] 获取用户资料`);

    const response = await apiClient.get('/user/profile');

    if (!response.ok) {
      throw new Error('获取用户资料失败');
    }

    return response.data;
  },

  /**
   * 更新用户资料
   */
  async updateUserProfile(profileData) {
    console.log(`[${Platform.OS}] 更新用户资料:`, profileData);

    const response = await apiClient.put('/user/profile', profileData);

    if (!response.ok) {
      throw new Error('更新用户资料失败');
    }

    return response.data;
  }
};

/**
 * 标签相关API
 */
export const tagApi = {
  /**
   * 根据步骤ID获取标签列表
   */
  async getTagsByStepId(stepId) {
    console.log(`[${Platform.OS}] 获取标签列表:`, { stepId });

    const response = await apiClient.get(`/tag/step/${stepId}`);

    if (!response.ok) {
      throw new Error('获取标签失败');
    }

    // 处理不同的响应格式
    let tags = [];
    const data = response.data;

    if (Array.isArray(data)) {
      tags = data;
    } else if (data?.data && Array.isArray(data.data)) {
      tags = data.data;
    } else if (data?.success && data?.data && Array.isArray(data.data)) {
      tags = data.data;
    }

    // 转换为前端需要的格式
    return tags.map(tag => ({
      id: tag.tag_id || tag.tagId || tag.id,
      text: tag.tag_text || tag.tagText || tag.text,
      color: tag.tag_color || tag.tagColor || tag.color || '#FF6B35'
    }));
  }
};

/**
 * 路由管理API
 */
export const routeApi = {
  lastRouteKey: 'last_route',
  routeHistoryKey: 'route_history',
  maxHistorySize: 10,

  /**
   * 保存当前路由
   */
  async saveCurrentRoute(route, params = {}) {
    try {
      const routeData = {
        route,
        params,
        timestamp: Date.now(),
        platform: Platform.OS
      };

      await apiClient.storage.setItem(this.lastRouteKey, JSON.stringify(routeData));
      await this.addToHistory(routeData);

      console.log(`[${Platform.OS}] 保存当前路由:`, routeData);
    } catch (error) {
      console.error(`[${Platform.OS}] 保存路由失败:`, error);
    }
  },

  /**
   * 获取上次访问的路由
   */
  async getLastRoute() {
    try {
      const routeData = await apiClient.storage.getItem(this.lastRouteKey);
      if (routeData) {
        const parsed = JSON.parse(routeData);
        console.log(`[${Platform.OS}] 获取上次路由:`, parsed);
        return parsed;
      }
    } catch (error) {
      console.error(`[${Platform.OS}] 获取上次路由失败:`, error);
    }
    return null;
  },

  /**
   * 添加到路由历史
   */
  async addToHistory(routeData) {
    try {
      const historyData = await apiClient.storage.getItem(this.routeHistoryKey);
      let history = historyData ? JSON.parse(historyData) : [];

      const lastRoute = history[history.length - 1];
      if (lastRoute && lastRoute.route === routeData.route) {
        return;
      }

      history.push(routeData);

      if (history.length > this.maxHistorySize) {
        history = history.slice(-this.maxHistorySize);
      }

      await apiClient.storage.setItem(this.routeHistoryKey, JSON.stringify(history));
    } catch (error) {
      console.error(`[${Platform.OS}] 添加路由历史失败:`, error);
    }
  },

  /**
   * 清除路由数据
   */
  async clearRouteData() {
    try {
      await apiClient.storage.removeItem(this.lastRouteKey);
      await apiClient.storage.removeItem(this.routeHistoryKey);
      console.log(`[${Platform.OS}] 路由数据已清除`);
    } catch (error) {
      console.error(`[${Platform.OS}] 清除路由数据失败:`, error);
    }
  },

  /**
   * 判断路由是否需要认证
   */
  isAuthRequiredRoute(route) {
    const authRequiredRoutes = [
      '/profile-setup',
      '/(dashboard)/plans',
      '/(dashboard)/profile',
      '/settings'
    ];

    return authRequiredRoutes.some(authRoute => route.includes(authRoute));
  },

  /**
   * 判断路由是否为游客可访问
   */
  isGuestAccessibleRoute(route) {
    const guestAccessibleRoutes = [
      '/(dashboard)',
      '/(dashboard)/index',
      '/welcome',
      '/login',
      '/register'
    ];

    return guestAccessibleRoutes.some(guestRoute =>
      route === guestRoute || route.startsWith(guestRoute)
    );
  }
};

/**
 * 通用API工具
 */
export const commonApi = {
  /**
   * 健康检查
   */
  async healthCheck() {
    const response = await apiClient.get('/health', { requireAuth: false });

    if (!response.ok) {
      throw new Error(`健康检查失败：${JSON.stringify(response.data)}`);
    }

    return response.data;
  },

  /**
   * 获取服务器时间
   */
  async getServerTime() {
    const response = await apiClient.get('/time', { requireAuth: false });

    if (!response.ok) {
      throw new Error(`获取服务器时间失败：${JSON.stringify(response.data)}`);
    }

    return response.data;
  }
};

/**
 * 帖子（Post）相关接口
 */
export const postApi = {
  /**
   * 获取帖子列表（支持按标签过滤）
   * @param {string} tag - 可选的标签过滤参数
   * @returns {Promise} 帖子列表
   */
  async getList(tag = '') {
    console.log(`[${Platform.OS}] 获取帖子列表:`, { tag });

    // 检查token是否存在
    const token = await apiClient.getAuthToken();
    if (!token) {
      throw new Error('请先登录后再查看帖子列表');
    }

    const query = tag ? `?tag=${encodeURIComponent(tag)}` : '';
    const response = await apiClient.get(`/community/list${query}`, {
      requireAuth: true
    });

    if (!response.ok) {
      throw new Error(`获取帖子列表失败：${JSON.stringify(response.data)}`);
    }

    return response.data;
  },

  /**
   * 根据ID获取单个帖子详情
   * @param {string|number} postId - 帖子ID
   * @returns {Promise} 帖子详情
   */
  async getById(postId) {
    console.log(`[${Platform.OS}] 获取帖子详情:`, { postId });

    if (!postId) throw new Error('帖子ID不能为空');

    // 检查token是否存在
    const token = await apiClient.getAuthToken();
    if (!token) {
      throw new Error('请先登录后再查看帖子详情');
    }

    // 由于后端没有单独的获取帖子详情接口，我们使用列表接口然后过滤
    // 这是一个临时解决方案，建议后端添加专门的帖子详情接口
    try {
      const response = await apiClient.get('/community/list', {
        requireAuth: true
      });

      if (!response.ok) {
        throw new Error(`获取帖子列表失败：${JSON.stringify(response.data)}`);
      }

      // 从列表中查找指定ID的帖子
      const posts = response.data?.data || response.data || [];
      const targetPost = posts.find(post => post.id == postId);

      if (!targetPost) {
        throw new Error('帖子不存在或已被删除');
      }

      console.log(`[${Platform.OS}] 从列表中找到目标帖子:`, targetPost);

      // 返回与其他API相同的格式
      return {
        code: response.data?.code || 0,
        message: response.data?.message || 'ok',
        data: targetPost
      };
    } catch (error) {
      console.error(`[${Platform.OS}] 获取帖子详情失败:`, error);
      throw error;
    }
  },

  /**
   * 获取评论列表（传入帖子ID）
   * @param {string|number} postId - 帖子ID
   * @returns {Promise} 评论列表
   */
  async getComment(postId) {
    console.log(`[${Platform.OS}] 获取帖子评论:`, { postId });

    if (!postId) throw new Error('帖子ID不能为空');

    // 检查token是否存在
    const token = await apiClient.getAuthToken();
    if (!token) {
      throw new Error('请先登录后再查看评论');
    }

    const response = await apiClient.get(`/community/comment?postId=${postId}`, {
      requireAuth: true
    });

    if (!response.ok) {
      throw new Error(`获取评论失败：${JSON.stringify(response.data)}`);
    }

    return response.data;
  },

  /**
   * 点赞帖子
   * @param {string|number} postId - 帖子ID
   * @returns {Promise} 点赞结果
   */
  async like(postId) {
    console.log(`[${Platform.OS}] 点赞帖子开始:`, { postId });

    if (!postId) throw new Error('帖子ID不能为空');

    // 检查token是否存在
    const token = await apiClient.getAuthToken();
    if (!token) {
      throw new Error('请先登录后再进行点赞操作');
    }

    try {
      const response = await apiClient.post(`/community/like/${postId}`, null, {
        requireAuth: true,
      });

      if (!response.ok) {
        console.error('点赞失败:', response);
        throw new Error(`点赞失败：${JSON.stringify(response.data)} (状态码: ${response.status})`);
      }

      return response.data;
    } catch (error) {
      console.error('点赞请求异常:', error);
      throw error;
    }
  },

  /**
   * 取消点赞
   * @param {string|number} postId - 帖子ID
   * @returns {Promise} 取消点赞结果
   */
  async unlike(postId) {
    console.log(`[${Platform.OS}] 取消点赞开始:`, { postId });

    if (!postId) throw new Error('帖子ID不能为空');

    // 检查token是否存在
    const token = await apiClient.getAuthToken();
    if (!token) {
      throw new Error('请先登录后再进行取消点赞操作');
    }

    // 使用 DELETE + 路径参数的方式
    const response = await apiClient.delete(`/community/like/${postId}`, {
      requireAuth: true
    });

    if (!response.ok) {
      throw new Error(`取消点赞失败：${JSON.stringify(response.data)} (状态码: ${response.status})`);
    }

    return response.data;
  },

  /**
   * 隐藏/显示帖子
   * @param {string|number} postId - 帖子ID
   * @param {boolean} isHidden - 是否隐藏
   * @returns {Promise} 操作结果
   */
  async toggleHidden(postId, isHidden) {
    console.log(`[${Platform.OS}] 切换帖子隐藏状态:`, { postId, isHidden });

    if (!postId) throw new Error('帖子ID不能为空');

    // 检查token是否存在
    const token = await apiClient.getAuthToken();
    if (!token) {
      throw new Error('请先登录后再进行操作');
    }

    console.log('📡 发送API请求:', {
      url: `/community/post/${postId}/hidden`,
      body: { isHidden },
      hasToken: !!token
    });

    const response = await apiClient.put(`/community/post/${postId}/hidden`,
      { isHidden },
      {
        requireAuth: true,
      }
    );

    console.log('📡 API响应:', {
      ok: response.ok,
      status: response.status,
      data: response.data
    });

    if (!response.ok) {
      throw new Error(`操作失败：${JSON.stringify(response.data)} (状态码: ${response.status})`);
    }

    return response.data;
  },

  /**
   * 发布评论
   * @param {string|number} postId - 帖子ID
   * @param {string} content - 评论内容
   * @returns {Promise} 发布结果
   */
  async addComment(postId, content) {
    console.log(`[${Platform.OS}] 发布评论:`, { postId, contentLength: content?.length });

    if (!postId) throw new Error('帖子ID不能为空');
    if (!content?.trim()) throw new Error('评论内容不能为空');

    // 检查token是否存在
    const token = await apiClient.getAuthToken();
    if (!token) {
      throw new Error('请先登录后再发布评论');
    }

    // 使用 POST /community/comment
    const requestData = {
      postId: postId,
      content: content.trim()
    };

    console.log(`[${Platform.OS}] 发送评论请求数据:`, requestData);
    console.log(`[${Platform.OS}] 请求URL: /community/comment`);
    console.log(`[${Platform.OS}] 请求头包含token:`, token ? `${token.substring(0, 20)}...` : 'null');

    try {
      const response = await apiClient.post('/community/comment', requestData, {
        requireAuth: true
      });

      console.log(`[${Platform.OS}] 评论API响应:`, {
        ok: response.ok,
        status: response.status,
        data: response.data
      });

      if (!response.ok) {
        // 提供更详细的错误信息
        let errorMessage = '发布评论失败';

        if (response.status === 500) {
          errorMessage = '服务器内部错误，请稍后重试';
        } else if (response.status === 401) {
          errorMessage = '认证失败，请重新登录';
        } else if (response.status === 403) {
          errorMessage = '权限不足，无法发布评论';
        } else if (response.status === 400) {
          errorMessage = '请求数据格式错误';
        }

        console.error(`[${Platform.OS}] 评论发布失败详情:`, {
          status: response.status,
          statusText: response.statusText,
          data: response.data,
          requestData
        });

        throw new Error(`${errorMessage}：${JSON.stringify(response.data)}`);
      }

      return response.data;
    } catch (error) {
      console.error(`[${Platform.OS}] 评论发布异常:`, error);
      throw error;
    }
  },

  /**
   * 发布帖子（支持内容、标签、媒体）
   * @param {Object} postData - 帖子数据
   * @param {string} postData.content - 帖子内容
   * @param {string[]} postData.tags - 标签数组
   * @param {Object[]} postData.media - 媒体文件数组
   * @returns {Promise} 发布结果
   */
  async createPost(postData) {
    console.log(`[${Platform.OS}] 发布帖子:`, {
      contentLength: postData?.content?.length,
      tagsCount: postData?.tags?.length,
      mediaCount: postData?.media?.length
    });

    if (!postData?.content?.trim()) {
      throw new Error('帖子内容不能为空');
    }

    // 检查token是否存在
    const token = await apiClient.getAuthToken();
    console.log(`[${Platform.OS}] 发布帖子时的token状态:`, token ? `${token.substring(0, 20)}...` : 'null');

    if (!token) {
      console.error(`[${Platform.OS}] 发布帖子失败: 未找到认证token`);
      throw new Error('请先登录后再发布帖子');
    }

    const validated = {
      title: postData.title || (postData.content ? postData.content.substring(0, 50) : '') || '新动态',
      userId: postData.userId || null, // 用户ID
      content: postData.content ? postData.content.trim() : '',
      imageUrls: postData.imageUrls || (postData.media ? postData.media.map(m => m.uri || m).join(',') : ''), // 兼容新旧字段名
      tag: postData.tag || (Array.isArray(postData.tags) ? postData.tags.join(',') : ''), // 兼容新旧字段名
      likeCount: postData.likeCount || 0, // 点赞数
      commentCount: postData.commentCount || 0, // 评论数
      viewCount: postData.viewCount || 0, // 浏览数
      visibility: postData.visibility || 'public'
    };

    const response = await apiClient.post('/community/post/create', validated, {
      requireAuth: true
    });

    if (!response.ok) {
      let errorMessage = '发布帖子失败';

      // 根据状态码提供具体的错误信息
      switch (response.status) {
        case 401:
          errorMessage = '认证失败，请重新登录';
          break;
        case 403:
          errorMessage = '权限不足，无法发布帖子';
          break;
        case 404:
          errorMessage = 'API端点不存在，请检查后端服务';
          break;
        case 422:
          errorMessage = '数据格式错误，请检查输入内容';
          break;
        case 500:
          errorMessage = '服务器内部错误，请稍后重试';
          break;
        default:
          if (response.data) {
            if (typeof response.data === 'string' && response.data.trim()) {
              errorMessage = response.data;
            } else if (response.data.message) {
              errorMessage = response.data.message;
            } else if (response.data.error) {
              errorMessage = response.data.error;
            } else {
              errorMessage = `服务器错误 (状态码: ${response.status})`;
            }
          } else {
            errorMessage = `网络错误 (状态码: ${response.status})`;
          }
      }

      console.error(`[${Platform.OS}] 发布帖子失败:`, {
        status: response.status,
        statusText: response.statusText,
        data: response.data,
        errorMessage
      });

      throw new Error(errorMessage);
    }

    return response.data;
  }
};

// 导出所有API服务
export default {
  auth: authApi,
  content: contentApi,
  profile: profileApi,
  tag: tagApi,
  route: routeApi,
  common: commonApi,
  post: postApi
};
