import { Platform } from 'react-native';

/**
 * 创建跨平台的阴影样式
 * @param {Object} options - 阴影配置选项
 * @param {string} options.shadowColor - 阴影颜色 (默认: '#000')
 * @param {Object} options.shadowOffset - 阴影偏移 (默认: { width: 0, height: 2 })
 * @param {number} options.shadowOpacity - 阴影透明度 (默认: 0.1)
 * @param {number} options.shadowRadius - 阴影半径 (默认: 4)
 * @param {number} options.elevation - Android阴影高度 (默认: 2)
 * @returns {Object} 跨平台阴影样式对象
 */
export const createShadow = ({
  shadowColor = '#000',
  shadowOffset = { width: 0, height: 2 },
  shadowOpacity = 0.1,
  shadowRadius = 4,
  elevation = 2
} = {}) => {
  if (Platform.OS === 'web') {
    // Web平台使用boxShadow
    const { width, height } = shadowOffset;
    const alpha = Math.round(shadowOpacity * 255).toString(16).padStart(2, '0');
    const shadowColorWithAlpha = shadowColor + alpha;
    
    return {
      boxShadow: `${width}px ${height}px ${shadowRadius}px ${shadowColorWithAlpha}`,
    };
  } else {
    // 移动端使用传统shadow属性
    return {
      shadowColor,
      shadowOffset,
      shadowOpacity,
      shadowRadius,
      elevation, // Android
    };
  }
};

/**
 * 预定义的常用阴影样式
 */
export const shadowPresets = {
  // 轻微阴影
  light: createShadow({
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1
  }),
  
  // 中等阴影
  medium: createShadow({
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2
  }),
  
  // 重阴影
  heavy: createShadow({
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4
  }),
  
  // 卡片阴影
  card: createShadow({
    shadowColor: 'rgba(0, 0, 0, 0.08)',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 1,
    shadowRadius: 16,
    elevation: 6
  }),
  
  // 侧边栏阴影
  sidebar: createShadow({
    shadowColor: 'rgba(0, 0, 0, 0.08)',
    shadowOffset: { width: 6, height: 0 },
    shadowOpacity: 1,
    shadowRadius: 16,
    elevation: 6
  })
};
