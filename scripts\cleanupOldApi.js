// scripts/cleanupOldApi.js - 清理旧API文件的脚本
const fs = require('fs');
const path = require('path');

// 可以安全删除的旧API文件
const OLD_API_FILES = [
  'lib/api.js',
  // 'lib/contentApi.js', // 保留作为兼容层
  // 'lib/userProfile.js', // 保留作为兼容层
  // 'lib/profileApi.js', // 保留作为兼容层
];

// 需要备份的文件
const BACKUP_FILES = [
  'lib/api.js',
  'lib/contentApi.js',
  'lib/userProfile.js',
  'lib/profileApi.js'
];

/**
 * 创建备份目录
 */
function createBackupDir() {
  const backupDir = path.join(__dirname, '..', 'backup_old_api');
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
    console.log('✅ 创建备份目录:', backupDir);
  }
  return backupDir;
}

/**
 * 备份文件
 */
function backupFiles() {
  const backupDir = createBackupDir();
  const projectRoot = path.resolve(__dirname, '..');
  
  console.log('📦 开始备份旧API文件...');
  
  BACKUP_FILES.forEach(filePath => {
    const fullPath = path.join(projectRoot, filePath);
    if (fs.existsSync(fullPath)) {
      const backupPath = path.join(backupDir, path.basename(filePath));
      fs.copyFileSync(fullPath, backupPath);
      console.log(`✅ 备份: ${filePath} -> backup_old_api/${path.basename(filePath)}`);
    } else {
      console.log(`⚠️  文件不存在: ${filePath}`);
    }
  });
}

/**
 * 删除旧API文件
 */
function deleteOldFiles() {
  const projectRoot = path.resolve(__dirname, '..');
  
  console.log('🗑️  开始删除旧API文件...');
  
  OLD_API_FILES.forEach(filePath => {
    const fullPath = path.join(projectRoot, filePath);
    if (fs.existsSync(fullPath)) {
      fs.unlinkSync(fullPath);
      console.log(`✅ 删除: ${filePath}`);
    } else {
      console.log(`⚠️  文件不存在: ${filePath}`);
    }
  });
}

/**
 * 显示迁移总结
 */
function showMigrationSummary() {
  console.log('\n🎉 API迁移完成！');
  console.log('\n📊 迁移总结:');
  console.log('✅ 已创建统一的API配置系统');
  console.log('✅ 已迁移主要业务文件');
  console.log('✅ 已保留向后兼容层');
  console.log('✅ 已备份旧API文件');
  
  console.log('\n🔧 新API系统文件:');
  console.log('• lib/apiConfig.js - 统一配置管理');
  console.log('• lib/apiClient.js - HTTP客户端');
  console.log('• lib/apiServices.js - 业务API服务');
  console.log('• lib/apiLegacy.js - 向后兼容层');
  
  console.log('\n📚 兼容层文件 (可选删除):');
  console.log('• lib/contentApi.js - 内容API兼容层');
  console.log('• lib/userProfile.js - 用户资料API兼容层');
  console.log('• lib/profileApi.js - 资料API兼容层');
  
  console.log('\n🚀 使用新API系统:');
  console.log('import apiServices from "../lib/apiServices";');
  console.log('const data = await apiServices.content.getIndexData();');
  console.log('const user = await apiServices.auth.login(username, password);');
  
  console.log('\n📖 详细文档: API_GUIDE.md');
}

/**
 * 主函数
 */
function main() {
  console.log('🔧 开始API清理流程...\n');
  
  try {
    // 1. 备份文件
    backupFiles();
    
    // 2. 删除旧文件（可选）
    if (process.argv.includes('--delete')) {
      deleteOldFiles();
    } else {
      console.log('\n💡 提示: 使用 --delete 参数来删除旧API文件');
      console.log('   例如: node scripts/cleanupOldApi.js --delete');
    }
    
    // 3. 显示总结
    showMigrationSummary();
    
  } catch (error) {
    console.error('❌ 清理过程中出现错误:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { main, backupFiles, deleteOldFiles };
