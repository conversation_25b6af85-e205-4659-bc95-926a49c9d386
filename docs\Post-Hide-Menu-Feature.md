# 帖子隐藏菜单功能实现

## 🎯 功能目标

在帖子卡片的右下角添加三个点的菜单按钮，点击后弹出菜单栏，提供隐藏/显示帖子的功能，与后端的isHidden字段连接。

## 🔧 实现内容

### 1. PostCard组件增强 ✅

#### 文件: `components/PostCard.jsx`

**新增PostMenu组件**:
```javascript
const PostMenu = ({ visible, onClose, post, onHidePost }) => {
  const handleHidePost = async () => {
    try {
      Alert.alert(
        '确认操作',
        post.isHidden ? '确定要显示这个帖子吗？' : '确定要隐藏这个帖子吗？',
        [
          {
            text: '取消',
            style: 'cancel',
          },
          {
            text: '确定',
            onPress: async () => {
              console.log('🔄 切换帖子隐藏状态:', { postId: post.id, currentHidden: post.isHidden });
              await onHidePost(post.id, !post.isHidden);
              onClose();
            },
          },
        ]
      );
    } catch (error) {
      console.error('❌ 操作失败:', error);
      Alert.alert('错误', '操作失败，请重试');
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableOpacity 
        style={styles.modalOverlay} 
        activeOpacity={1} 
        onPress={onClose}
      >
        <View style={styles.menuContainer}>
          <TouchableOpacity 
            style={styles.menuItem} 
            onPress={handleHidePost}
          >
            <ThemedText style={styles.menuItemText}>
              {post.isHidden ? '显示帖子' : '隐藏帖子'}
            </ThemedText>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.menuItem, styles.cancelMenuItem]} 
            onPress={onClose}
          >
            <ThemedText style={[styles.menuItemText, styles.cancelMenuItemText]}>
              取消
            </ThemedText>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};
```

**菜单按钮替换**:
```javascript
// 修改前 - 转发按钮
<TouchableOpacity
  style={styles.interactionBtnRight}
  onPress={() => onForwardPress && onForwardPress(post)}
>
  <Image source={forwardIcon} style={{ width: 20, height: 20 }} />
</TouchableOpacity>

// 修改后 - 菜单按钮
<TouchableOpacity
  style={styles.interactionBtnRight}
  onPress={() => setShowMenu(true)}
>
  <ThemedText style={styles.menuButtonText}>⋯</ThemedText>
</TouchableOpacity>
```

**菜单状态管理**:
```javascript
const PostCard = ({ post, onCommentPress, onForwardPress, onLikeToggle, onPostPress, onHidePost }) => {
  const [showMenu, setShowMenu] = useState(false);
  
  // ... 其他代码
  
  return (
    <TouchableOpacity>
      {/* 帖子内容 */}
      
      {/* 帖子菜单 */}
      <PostMenu
        visible={showMenu}
        onClose={() => setShowMenu(false)}
        post={post}
        onHidePost={onHidePost}
      />
    </TouchableOpacity>
  );
};
```

**菜单样式**:
```javascript
// 菜单按钮样式
menuButtonText: {
  fontSize: 18,
  color: '#666',
  fontWeight: 'bold',
},

// 菜单弹窗样式
modalOverlay: {
  flex: 1,
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  justifyContent: 'center',
  alignItems: 'center',
},
menuContainer: {
  backgroundColor: '#fff',
  borderRadius: 12,
  minWidth: 200,
  paddingVertical: 8,
  shadowColor: '#000',
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.25,
  shadowRadius: 8,
  elevation: 5,
},
menuItem: {
  paddingVertical: 16,
  paddingHorizontal: 20,
  borderBottomWidth: 1,
  borderBottomColor: '#f0f0f0',
},
cancelMenuItem: {
  borderBottomWidth: 0,
  borderTopWidth: 1,
  borderTopColor: '#f0f0f0',
  marginTop: 8,
},
menuItemText: {
  fontSize: 16,
  color: '#333',
  textAlign: 'center',
},
cancelMenuItemText: {
  color: '#999',
},
```

### 2. API服务扩展 ✅

#### 文件: `lib/apiServices.js`

**新增隐藏帖子API**:
```javascript
/**
 * 隐藏/显示帖子
 * @param {string|number} postId - 帖子ID
 * @param {boolean} isHidden - 是否隐藏
 * @returns {Promise} 操作结果
 */
async toggleHidden(postId, isHidden) {
  console.log(`[${Platform.OS}] 切换帖子隐藏状态:`, { postId, isHidden });

  if (!postId) throw new Error('帖子ID不能为空');

  // 检查token是否存在
  const token = await apiClient.getAuthToken();
  if (!token) {
    throw new Error('请先登录后再进行操作');
  }

  const response = await apiClient.put(`/community/post/${postId}/hidden`, 
    { isHidden }, 
    {
      requireAuth: true,
    }
  );

  if (!response.ok) {
    throw new Error(`操作失败：${JSON.stringify(response.data)} (状态码: ${response.status})`);
  }

  return response.data;
},
```

### 3. 社区页面集成 ✅

#### 文件: `app/(dashboard)/community.jsx`

**隐藏帖子处理函数**:
```javascript
// 处理隐藏帖子
const handleHidePost = async (postId, isHidden) => {
  try {
    console.log('🔄 开始隐藏/显示帖子:', { postId, isHidden });

    // 调用API
    await postApi.toggleHidden(postId, isHidden);
    console.log('✅ 帖子隐藏状态更新成功');

    // 更新本地状态
    setPosts(prevPosts => 
      prevPosts.map(post => 
        post.id === postId 
          ? { ...post, isHidden: isHidden }
          : post
      )
    );

    // 显示成功提示
    console.log(`帖子已${isHidden ? '隐藏' : '显示'}`);
    
  } catch (error) {
    console.error('❌ 隐藏帖子操作失败:', error);
    throw error; // 让PostCard组件处理错误
  }
};
```

**PostCard组件调用更新**:
```javascript
<PostCard
  key={post.id}
  post={post}
  onPostPress={() => router.push(`/Community/Post?id=${post.id}`)}
  onLikeToggle={async (isLiked) => {
    // ... 点赞逻辑
  }}
  onCommentPress={wrapGuestAction(() => console.log('评论', post.id), 'community-interaction')}
  onForwardPress={wrapGuestAction(() => console.log('转发', post.id), 'community-interaction')}
  onHidePost={wrapGuestAction(handleHidePost, 'community-interaction')}
/>
```

## 🎨 用户体验

### 1. 交互流程
1. **点击菜单按钮** - 用户点击帖子右下角的三个点按钮
2. **显示菜单** - 弹出半透明遮罩的菜单弹窗
3. **选择操作** - 显示"隐藏帖子"或"显示帖子"选项
4. **确认操作** - 弹出确认对话框
5. **执行操作** - 调用API并更新UI状态
6. **反馈结果** - 显示操作成功或失败的提示

### 2. 视觉设计
- ✅ **菜单按钮** - 使用三个点符号(⋯)，简洁明了
- ✅ **弹窗设计** - 圆角白色背景，半透明遮罩
- ✅ **菜单项** - 清晰的文字说明，适当的内边距
- ✅ **取消按钮** - 灰色文字，明确的分割线

### 3. 状态管理
- ✅ **动态文字** - 根据当前隐藏状态显示"隐藏帖子"或"显示帖子"
- ✅ **本地更新** - API调用成功后立即更新本地状态
- ✅ **错误处理** - API失败时显示错误提示

## 🔧 技术特性

### API调用
```
PUT /community/post/{postId}/hidden
Body: { "isHidden": true/false }
Headers: Authorization: Bearer {token}
```

### 数据流程
```
用户点击 → 显示菜单 → 确认操作 → API调用 → 更新状态 → UI刷新
```

### 错误处理
- ✅ **网络错误** - 显示"操作失败，请重试"
- ✅ **权限错误** - 通过wrapGuestAction处理游客权限
- ✅ **参数错误** - API层面验证postId和isHidden参数

## 🚀 部署建议

### 测试验证
1. **菜单显示** - 验证三个点按钮点击后菜单正确显示
2. **隐藏功能** - 测试隐藏帖子API调用和状态更新
3. **显示功能** - 测试显示帖子API调用和状态更新
4. **权限控制** - 验证游客用户的权限提示
5. **错误处理** - 测试网络异常时的错误提示

### 功能扩展
- [ ] 添加更多菜单选项（举报、收藏等）
- [ ] 支持批量操作
- [ ] 添加操作历史记录
- [ ] 管理员专用功能

---

**功能完成时间**: 2024-08-02  
**影响范围**: 帖子卡片交互功能  
**技术改进**: 菜单系统 + API集成 + 状态管理
