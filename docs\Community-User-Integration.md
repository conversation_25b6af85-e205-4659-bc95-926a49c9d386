# 社区帖子用户信息集成

## 🎯 功能目标

将社区页面的帖子用户名从硬编码改为调用后端API获取真实的用户信息，包括用户名、头像等。

## 🔄 修改内容

### 1. 社区页面数据获取优化 ✅

#### 文件: `app/(dashboard)/community.jsx`

**新增导入**:
```javascript
import { postApi, authApi } from '../../lib/apiServices';
```

**fetchPosts函数重构**:
```javascript
const fetchPosts = async (tag = null) => {
  try {
    setLoading(true);
    setError(null);

    // 1. 获取帖子列表
    const response = await postApi.getList(tag);
    const postsData = response.data || [];

    // 2. 提取所有唯一的userId
    const userIds = [...new Set(postsData.map(post => post.userId).filter(Boolean))];
    
    // 3. 批量获取用户信息
    let usersMap = {};
    if (userIds.length > 0) {
      try {
        const users = await authApi.getUsersByIds(userIds);
        usersMap = users.reduce((map, user) => {
          map[user.id] = user;
          return map;
        }, {});
      } catch (userError) {
        console.warn('获取用户信息失败，使用默认显示:', userError);
      }
    }

    // 4. 格式化帖子数据，包含用户信息
    const formatted = postsData.map(post => ({
      // ... 其他字段
      userId: post.userId,
      user: usersMap[post.userId]?.userAccount || usersMap[post.userId]?.userName || post.userId || 'Unknown',
      userAvatar: usersMap[post.userId]?.userAvatar || null,
      userInfo: usersMap[post.userId] || null,
    }));

    setPosts(formatted);
  } catch (err) {
    setError(err.message || '获取帖子失败');
  } finally {
    setLoading(false);
  }
};
```

### 2. PostCard组件用户显示优化 ✅

#### 文件: `components/PostCard.jsx`

**动态头像获取**:
```javascript
const getUserAvatar = (post) => {
  // 优先使用用户的真实头像
  if (post.userAvatar) {
    return (
      <Image 
        source={{ uri: post.userAvatar }} 
        style={styles.avatar}
        defaultSource={require('../assets/Community_image/AvatarOne.png')}
      />
    );
  }
  
  // 如果没有头像，根据用户名使用默认头像或生成字母头像
  const user = post.user || post.userId;
  switch (user) {
    case 'Jessica':
    case 'Kin':
    case 'Caaary':
      return <Image source={avatarMap[user]} style={styles.avatar} />;
    default:
      // 根据用户ID生成颜色和字母头像
      const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
      const colorIndex = (user || '').length % colors.length;
      return (
        <View style={[styles.avatar, { backgroundColor: colors[colorIndex] }]}>
          <ThemedText style={styles.avatarText}>
            {(user || 'U').charAt(0).toUpperCase()}
          </ThemedText>
        </View>
      );
  }
};
```

**用户名显示优化**:
```javascript
<ThemedText style={styles.username}>
  {post.user || post.userId || 'Unknown'}
</ThemedText>
```

## 🔧 技术实现

### 数据流程
1. **获取帖子** → `postApi.getList(tag)`
2. **提取用户ID** → `userIds = [...new Set(posts.map(p => p.userId))]`
3. **批量获取用户** → `authApi.getUsersByIds(userIds)`
4. **创建用户映射** → `usersMap[userId] = userInfo`
5. **格式化帖子数据** → 包含用户信息的完整帖子对象

### 用户信息字段映射
| 后端字段 | 前端使用 | 说明 |
|----------|----------|------|
| `user.userAccount` | `post.user` | 优先使用用户账号 |
| `user.userName` | `post.user` | 备选用户名 |
| `user.userAvatar` | `post.userAvatar` | 用户头像URL |
| `user.id` | `post.userId` | 用户ID |

### 头像显示策略
1. **真实头像** - 如果用户有上传头像，显示网络图片
2. **默认头像** - 对于特定用户名（Jessica, Kin, Caaary）使用预设头像
3. **字母头像** - 其他用户生成彩色字母头像

## 🎨 视觉改进

### 1. 动态头像
- ✅ 支持网络头像加载
- ✅ 头像加载失败时显示默认头像
- ✅ 彩色字母头像作为备选方案

### 2. 字母头像生成
```javascript
// 6种颜色循环使用
const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
const colorIndex = (user || '').length % colors.length;

// 显示用户名首字母
{(user || 'U').charAt(0).toUpperCase()}
```

### 3. 用户名显示优先级
1. `userAccount` (用户账号)
2. `userName` (用户名)
3. `userId` (用户ID)
4. `'Unknown'` (默认值)

## 📱 用户体验

### 加载状态
- **批量获取** - 一次性获取所有用户信息，减少请求次数
- **错误容错** - 用户信息获取失败时降级显示userId
- **缓存友好** - 相同用户在多个帖子中只需获取一次信息

### 性能优化
- **去重处理** - `[...new Set(userIds)]` 避免重复请求
- **异步处理** - 用户信息获取不阻塞帖子显示
- **错误隔离** - 用户信息获取失败不影响帖子列表显示

## 🔍 API调用示例

### 获取帖子列表
```javascript
// 请求
GET /community/post/list

// 响应
{
  "data": [
    {
      "id": 1,
      "userId": "user123",
      "title": "学习心得",
      "content": "今天学习了新知识...",
      "createdAt": "2024-08-02T10:00:00Z"
    }
  ]
}
```

### 批量获取用户信息
```javascript
// 请求
POST /auth/users/batch
{
  "userIds": ["user123", "user456", "user789"]
}

// 响应
[
  {
    "id": "user123",
    "userAccount": "student123",
    "userName": "学习达人",
    "userAvatar": "https://example.com/avatar123.jpg"
  }
]
```

## 🚀 部署建议

### 测试验证
1. **用户信息显示** - 验证真实用户名和头像显示
2. **降级处理** - 测试API失败时的降级显示
3. **性能测试** - 验证批量获取用户信息的性能

### 监控指标
- 用户信息获取成功率
- 头像加载成功率
- 页面加载时间

---

**集成完成时间**: 2024-08-02  
**影响范围**: 社区页面帖子用户信息显示  
**技术改进**: 真实用户数据 + 动态头像 + 性能优化
