// components/LoginDebugger.jsx - 登录流程调试工具
import React, { useState } from 'react';
import { View, TouchableOpacity, ScrollView, Platform } from 'react-native';
import ThemedText from './ThemedText';
import ThemedTextInput from './ThemedTextInput';
import apiServices from '../lib/apiServices';
import { checkLoginStatus } from '../lib/authUtils';

const LoginDebugger = () => {
  const [userAccount, setUserAccount] = useState('wsy');
  const [userPassword, setUserPassword] = useState('********');
  const [loading, setLoading] = useState(false);
  const [debugInfo, setDebugInfo] = useState('');
  const [loginResult, setLoginResult] = useState(null);

  // 调试登录流程
  const debugLogin = async () => {
    setLoading(true);
    setDebugInfo('');
    setLoginResult(null);

    let debugLog = [];
    
    try {
      debugLog.push(`[${Platform.OS}] 开始调试登录流程`);
      debugLog.push(`账号: ${userAccount}`);
      debugLog.push(`密码: ${userPassword.replace(/./g, '*')}`);
      debugLog.push('');

      // 1. 检查登录前状态
      debugLog.push('=== 登录前状态检查 ===');
      const beforeStatus = await checkLoginStatus();
      debugLog.push(`登录状态: ${beforeStatus.isLoggedIn ? '已登录' : '未登录'}`);
      debugLog.push(`Token: ${beforeStatus.token ? '存在' : '不存在'}`);
      debugLog.push('');

      // 2. 执行登录
      debugLog.push('=== 执行登录 ===');
      const loginResponse = await apiServices.auth.login(userAccount, userPassword);
      debugLog.push('登录API响应:');
      debugLog.push(JSON.stringify(loginResponse, null, 2));
      debugLog.push('');

      // 3. 检查登录后状态
      debugLog.push('=== 登录后状态检查 ===');
      const afterStatus = await checkLoginStatus();
      debugLog.push(`登录状态: ${afterStatus.isLoggedIn ? '已登录' : '未登录'}`);
      debugLog.push(`Token: ${afterStatus.token ? '存在' : '不存在'}`);
      if (afterStatus.user) {
        debugLog.push(`用户信息: ${JSON.stringify(afterStatus.user, null, 2)}`);
      }
      debugLog.push('');

      // 4. 验证token
      debugLog.push('=== Token验证 ===');
      try {
        const userInfo = await apiServices.auth.getCurrentUser();
        debugLog.push('Token验证成功:');
        debugLog.push(JSON.stringify(userInfo, null, 2));
      } catch (tokenError) {
        debugLog.push(`Token验证失败: ${tokenError.message}`);
      }

      setLoginResult({
        success: true,
        response: loginResponse,
        beforeStatus,
        afterStatus
      });

    } catch (error) {
      debugLog.push('');
      debugLog.push('=== 登录失败 ===');
      debugLog.push(`错误: ${error.message}`);
      debugLog.push(`错误堆栈: ${error.stack}`);

      setLoginResult({
        success: false,
        error: error.message
      });
    } finally {
      setDebugInfo(debugLog.join('\n'));
      setLoading(false);
    }
  };

  // 测试token保存
  const testTokenSave = async () => {
    setLoading(true);
    try {
      const testToken = 'test-token-' + Date.now();
      console.log(`[${Platform.OS}] 测试保存token:`, testToken);
      
      await apiServices.auth.logout(); // 先清除
      const saveResult = await apiServices.auth.login.saveAuthToken?.(testToken);
      
      const retrievedToken = await apiServices.auth.login.getAuthToken?.();
      
      setDebugInfo(`测试Token保存:
保存结果: ${saveResult}
保存的Token: ${testToken}
获取的Token: ${retrievedToken}
是否一致: ${testToken === retrievedToken}`);
      
    } catch (error) {
      setDebugInfo(`Token保存测试失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={{ padding: 20, backgroundColor: '#f8f9fa' }}>
      <ThemedText style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 20 }}>
        登录流程调试工具
      </ThemedText>

      {/* 登录表单 */}
      <View style={{ marginBottom: 20 }}>
        <ThemedText style={{ marginBottom: 5 }}>用户账号:</ThemedText>
        <ThemedTextInput
          value={userAccount}
          onChangeText={setUserAccount}
          placeholder="输入用户账号"
          style={{ marginBottom: 10 }}
        />
        
        <ThemedText style={{ marginBottom: 5 }}>密码:</ThemedText>
        <ThemedTextInput
          value={userPassword}
          onChangeText={setUserPassword}
          placeholder="输入密码"
          secureTextEntry
          style={{ marginBottom: 15 }}
        />
      </View>

      {/* 操作按钮 */}
      <View style={{ flexDirection: 'row', marginBottom: 20 }}>
        <TouchableOpacity
          onPress={debugLogin}
          disabled={loading}
          style={[
            buttonStyle,
            { 
              backgroundColor: loading ? '#6c757d' : '#007bff',
              marginRight: 10,
              flex: 1
            }
          ]}
        >
          <ThemedText style={buttonTextStyle}>
            {loading ? '调试中...' : '调试登录'}
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={testTokenSave}
          disabled={loading}
          style={[
            buttonStyle,
            { 
              backgroundColor: loading ? '#6c757d' : '#28a745',
              flex: 1
            }
          ]}
        >
          <ThemedText style={buttonTextStyle}>
            测试Token
          </ThemedText>
        </TouchableOpacity>
      </View>

      {/* 结果显示 */}
      {loginResult && (
        <View style={{
          backgroundColor: loginResult.success ? '#d4edda' : '#f8d7da',
          padding: 15,
          borderRadius: 5,
          marginBottom: 15,
          borderLeftWidth: 4,
          borderLeftColor: loginResult.success ? '#28a745' : '#dc3545'
        }}>
          <ThemedText style={{
            color: loginResult.success ? '#155724' : '#721c24',
            fontWeight: 'bold',
            marginBottom: 10
          }}>
            {loginResult.success ? '✅ 登录成功' : '❌ 登录失败'}
          </ThemedText>
          
          {loginResult.success && loginResult.response && (
            <View>
              <ThemedText style={{ color: '#155724', fontSize: 12, marginBottom: 5 }}>
                响应结构: {loginResult.response.code === 0 ? '标准结构' : '非标准结构'}
              </ThemedText>
              <ThemedText style={{ color: '#155724', fontSize: 12, marginBottom: 5 }}>
                用户数据: {loginResult.response.data ? '存在' : '缺失'}
              </ThemedText>
              <ThemedText style={{ color: '#155724', fontSize: 12, marginBottom: 5 }}>
                Token状态: {loginResult.afterStatus?.token ? '已保存' : '未保存'}
              </ThemedText>
            </View>
          )}
          
          {!loginResult.success && (
            <ThemedText style={{ color: '#721c24', fontSize: 12 }}>
              错误: {loginResult.error}
            </ThemedText>
          )}
        </View>
      )}

      {/* 详细调试信息 */}
      {debugInfo && (
        <View style={{
          backgroundColor: '#ffffff',
          padding: 15,
          borderRadius: 5,
          borderWidth: 1,
          borderColor: '#dee2e6'
        }}>
          <ThemedText style={{
            fontSize: 11,
            fontFamily: 'monospace',
            color: '#495057',
            lineHeight: 16
          }}>
            {debugInfo}
          </ThemedText>
        </View>
      )}

      {/* 使用说明 */}
      <View style={{
        backgroundColor: '#e9ecef',
        padding: 15,
        borderRadius: 5,
        marginTop: 15
      }}>
        <ThemedText style={{ fontSize: 12, color: '#6c757d' }}>
          💡 调试说明：
        </ThemedText>
        <ThemedText style={{ fontSize: 11, color: '#6c757d', marginTop: 5 }}>
          • 调试登录：完整测试登录流程，检查每个步骤{'\n'}
          • 测试Token：单独测试token保存和获取功能{'\n'}
          • 查看详细日志了解问题所在
        </ThemedText>
      </View>
    </ScrollView>
  );
};

const buttonStyle = {
  padding: 12,
  borderRadius: 6,
  alignItems: 'center',
  justifyContent: 'center'
};

const buttonTextStyle = {
  color: 'white',
  fontWeight: 'bold',
  fontSize: 14
};

export default LoginDebugger;
