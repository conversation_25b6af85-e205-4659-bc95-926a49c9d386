import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, Image, TextInput, Alert, Animated, Easing, KeyboardAvoidingView, Platform } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import ThemedText from "../../components/ThemedText";
import ThemedView from "../../components/ThemedView";
import { Colors } from "../../constants/Colors";
import { useGuestPermission } from '../../hooks/useGuestPermission';
import GuestPermissionModal from '../../components/GuestPermissionModal';
import { postApi, authApi } from '../../lib/apiServices';

// 导入图标
const likeIcon = require('../../assets/Like33.png');
const commentIcon = require('../../assets/Comments.png');
const forwardIcon = require('../../assets/Many.png');
const backIcon = require('../../assets/Arrows_left.png');

// 导入头像图片
const avatarJessica = require('../../assets/Community_image/AvatarOne.png');
const avatarKin = require('../../assets/Community_image/AvatarTwo.png');
const avatarCaaary = require('../../assets/Community_image/AvatarThree.png');

// 带动画的点赞按钮组件
const AnimatedLikeButton = ({ isLiked: initialLiked, likeCount: initialCount, onLikeToggle }) => {
  const [isLiked, setIsLiked] = useState(initialLiked);
  const [likeCount, setLikeCount] = useState(initialCount);
  const [isAnimating, setIsAnimating] = useState(false);

  // 动画值
  const shakeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const particleAnims = useRef(Array.from({ length: 6 }, () => ({
    translateX: new Animated.Value(0),
    translateY: new Animated.Value(0),
    opacity: new Animated.Value(0),
    scale: new Animated.Value(1)
  }))).current;

  // 点赞动画
  const animateLike = () => {
    if (isAnimating) return;
    setIsAnimating(true);

    // 抖动动画
    Animated.sequence([
      Animated.timing(shakeAnim, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnim, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnim, { toValue: 5, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnim, { toValue: 0, duration: 50, useNativeDriver: true })
    ]).start();

    // 缩放动画
    Animated.sequence([
      Animated.timing(scaleAnim, { toValue: 1.3, duration: 100, useNativeDriver: true }),
      Animated.timing(scaleAnim, { toValue: 1, duration: 100, useNativeDriver: true })
    ]).start();

    // 粒子散发动画
    const particleAnimations = particleAnims.map((particle, index) => {
      const angle = (index * 60) * Math.PI / 180;
      const distance = 30;
      const targetX = Math.cos(angle) * distance;
      const targetY = Math.sin(angle) * distance;

      return Animated.parallel([
        Animated.timing(particle.translateX, { toValue: targetX, duration: 400, easing: Easing.out(Easing.quad), useNativeDriver: true }),
        Animated.timing(particle.translateY, { toValue: targetY, duration: 400, easing: Easing.out(Easing.quad), useNativeDriver: true }),
        Animated.sequence([
          Animated.timing(particle.opacity, { toValue: 1, duration: 100, useNativeDriver: true }),
          Animated.timing(particle.opacity, { toValue: 0, duration: 300, useNativeDriver: true })
        ]),
        Animated.sequence([
          Animated.timing(particle.scale, { toValue: 1.2, duration: 100, useNativeDriver: true }),
          Animated.timing(particle.scale, { toValue: 0, duration: 300, useNativeDriver: true })
        ])
      ]);
    });

    Animated.parallel(particleAnimations).start(() => {
      particleAnims.forEach(particle => {
        particle.translateX.setValue(0);
        particle.translateY.setValue(0);
        particle.opacity.setValue(0);
        particle.scale.setValue(1);
      });
      setIsAnimating(false);
    });
  };

  // 处理点赞
  const handleLike = async () => {
    if (isAnimating) return;

    const originalIsLiked = isLiked;
    const originalLikeCount = likeCount;

    try {
      if (!isLiked) {
        setIsAnimating(true);
        animateLike();
        setIsLiked(true);
        setLikeCount(prev => prev + 1);

        // 调用点赞API (需要传入postId)
        if (onLikeToggle) {
          await onLikeToggle(true);
        }
      } else {
        setIsLiked(false);
        setLikeCount(prev => prev - 1);

        // 调用取消点赞API
        if (onLikeToggle) {
          await onLikeToggle(false);
        }
      }

    } catch (error) {
      console.error('点赞操作失败:', error);
      setIsLiked(originalIsLiked);
      setLikeCount(originalLikeCount);
    } finally {
      setIsAnimating(false);
    }
  };

  return (
    <TouchableOpacity style={styles.likeContainer} onPress={handleLike}>
      {/* 粒子效果 */}
      {particleAnims.map((particle, index) => (
        <Animated.View
          key={index}
          style={[
            styles.particle,
            {
              transform: [
                { translateX: particle.translateX },
                { translateY: particle.translateY },
                { scale: particle.scale }
              ],
              opacity: particle.opacity
            }
          ]}
        />
      ))}

      {/* 点赞按钮 */}
      <Animated.View
        style={[
          styles.likeButton,
          {
            transform: [
              { translateX: shakeAnim },
              { scale: scaleAnim }
            ]
          }
        ]}
      >
        <Image
          source={likeIcon}
          style={[
            styles.likeIcon,
            isLiked && styles.likeIconActive
          ]}
        />
      </Animated.View>

      <ThemedText style={[
        styles.likeCount,
        isLiked && styles.likeCountActive
      ]}>
        {likeCount}
      </ThemedText>
    </TouchableOpacity>
  );
};

const Post = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const [post, setPost] = useState(null);
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentTime, setCurrentTime] = useState(new Date());

  // 权限管理
  const {
    wrapGuestAction,
    showPermissionModal,
    currentFeature,
    handleRegister,
    closePermissionModal
  } = useGuestPermission();

  // 实时更新时间显示
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // 每分钟更新一次

    return () => clearInterval(timer);
  }, []);

  // 格式化时间显示
  const formatTimeFromNow = (createTime) => {
    if (!createTime) return '未知时间';

    const createDate = new Date(createTime);
    const now = currentTime;
    const diffMs = now.getTime() - createDate.getTime();

    // 如果时间差为负数（未来时间），显示刚刚
    if (diffMs < 0) return '刚刚';

    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);
    const diffWeeks = Math.floor(diffDays / 7);
    const diffMonths = Math.floor(diffDays / 30);
    const diffYears = Math.floor(diffDays / 365);

    if (diffSeconds < 60) return '刚刚';
    if (diffMinutes < 60) return `${diffMinutes}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;
    if (diffWeeks < 4) return `${diffWeeks}周前`;
    if (diffMonths < 12) return `${diffMonths}个月前`;
    return `${diffYears}年前`;
  };

  // 获取帖子详情和评论
  useEffect(() => {
    console.log('📋 Post页面参数:', params);
    console.log('📋 帖子ID:', params.id);

    const fetchPostDetail = async () => {
      if (!params.id) {
        console.log('❌ 帖子ID不存在，params:', params);
        setError('帖子ID不存在');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        console.log('🔄 开始获取帖子详情:', params.id);

        // 1. 获取帖子详情
        const postResponse = await postApi.getById(params.id);
        const postData = postResponse.data;

        if (!postData) {
          throw new Error('帖子不存在');
        }

        // 2. 获取帖子作者信息
        let userInfo = null;
        if (postData.userId) {
          try {
            userInfo = await authApi.getUserById(postData.userId);
          } catch (userError) {
            console.warn('获取用户信息失败:', userError);
          }
        }

        // 3. 格式化帖子数据
        const formattedPost = {
          id: postData.id,
          title: postData.title,
          content: postData.content,
          imageUrls: postData.imageUrls ? postData.imageUrls.split(',').filter(Boolean) : [],
          tag: postData.tag,
          likeCount: postData.likeCount || 0,
          commentCount: postData.commentCount || 0,
          viewCount: postData.viewCount || 0,
          createTime: postData.createTime,
          userId: postData.userId,
          user: userInfo?.userAccount || userInfo?.userName || postData.userId || 'Unknown',
          userAvatar: userInfo?.userAvatar || null,
          userInfo: userInfo || null,
          hasMedia: postData.imageUrls && postData.imageUrls.trim().length > 0,
        };

        console.log('✅ 帖子详情获取成功:', formattedPost);
        setPost(formattedPost);

        // 4. 获取评论列表
        try {
          const commentsResponse = await postApi.getComment(params.id);
          const commentsData = commentsResponse.data || [];

          // 5. 批量获取评论作者信息
          const commentUserIds = [...new Set(commentsData.map(comment => comment.userId).filter(Boolean))];
          let commentUsersMap = {};

          if (commentUserIds.length > 0) {
            try {
              const commentUsers = await authApi.getUsersByIds(commentUserIds);
              commentUsersMap = commentUsers.reduce((map, user) => {
                map[user.id] = user;
                return map;
              }, {});
            } catch (userError) {
              console.warn('获取评论用户信息失败:', userError);
            }
          }

          // 6. 格式化评论数据
          const formattedComments = commentsData.map(comment => ({
            id: comment.id,
            content: comment.content,
            createTime: comment.createTime,
            likeCount: comment.likeCount || 0,
            userId: comment.userId,
            user: commentUsersMap[comment.userId]?.userAccount || commentUsersMap[comment.userId]?.userName || comment.userId || 'Unknown',
            userAvatar: commentUsersMap[comment.userId]?.userAvatar || null,
            userInfo: commentUsersMap[comment.userId] || null,
            isLiked: false, // TODO: 需要从API获取当前用户的点赞状态
          }));

          console.log('✅ 评论列表获取成功:', formattedComments);
          setComments(formattedComments);

        } catch (commentsError) {
          console.warn('获取评论失败:', commentsError);
          setComments([]);
        }

      } catch (err) {
        console.error('❌ 获取帖子详情失败:', err);
        setError(err.message || '获取帖子详情失败');
      } finally {
        setLoading(false);
      }
    };

    fetchPostDetail();
  }, [params.id]);

  // 获取用户头像
  const getUserAvatar = (userObj) => {
    // 支持传入用户对象或用户名字符串
    const user = typeof userObj === 'string' ? userObj : userObj?.user;
    const userAvatar = typeof userObj === 'object' ? userObj?.userAvatar : null;

    // 优先使用用户的真实头像
    if (userAvatar) {
      return (
        <Image
          source={{ uri: userAvatar }}
          style={styles.avatar}
          defaultSource={avatarJessica}
        />
      );
    }

    // 如果没有头像，根据用户名使用默认头像或生成字母头像
    switch (user) {
      case 'Jessica':
        return <Image source={avatarJessica} style={styles.avatar} />;
      case 'Kin':
        return <Image source={avatarKin} style={styles.avatar} />;
      case 'Caaary':
        return <Image source={avatarCaaary} style={styles.avatar} />;
      default:
        // 根据用户ID生成颜色和字母头像
        const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
        const colorIndex = (user || '').length % colors.length;
        return (
          <View style={[styles.avatar, { backgroundColor: colors[colorIndex], justifyContent: 'center', alignItems: 'center' }]}>
            <ThemedText style={{ color: '#fff', fontSize: 16, fontWeight: 'bold' }}>
              {(user || 'U').charAt(0).toUpperCase()}
            </ThemedText>
          </View>
        );
    }
  };

  // 处理发布评论
  const handleSubmitComment = wrapGuestAction(async () => {
    if (!newComment.trim()) {
      Alert.alert('提示', '请输入评论内容');
      return;
    }

    if (!post?.id) {
      Alert.alert('错误', '帖子信息异常');
      return;
    }

    setIsSubmitting(true);
    try {
      console.log('🔄 开始发布评论:', { postId: post.id, content: newComment.trim() });

      // 调用API发布评论
      const result = await postApi.addComment(post.id, newComment.trim());
      console.log('✅ 评论发布成功:', result);

      // 获取当前用户信息
      let currentUser = null;
      try {
        currentUser = await authApi.getCurrentUser();
      } catch (userError) {
        console.warn('获取当前用户信息失败:', userError);
      }

      // 创建新评论对象
      const newCommentObj = {
        id: result.data?.id || Date.now().toString(),
        content: newComment.trim(),
        createTime: new Date().toISOString(),
        likeCount: 0,
        userId: currentUser?.id || 'current_user',
        user: currentUser?.userAccount || currentUser?.userName || '我',
        userAvatar: currentUser?.userAvatar || null,
        userInfo: currentUser || null,
        isLiked: false,
      };

      // 添加到评论列表顶部
      setComments(prev => [newCommentObj, ...prev]);

      // 更新帖子的评论数
      setPost(prev => prev ? {
        ...prev,
        commentCount: (prev.commentCount || 0) + 1
      } : prev);

      setNewComment('');
      Alert.alert('成功', '评论发布成功！');

    } catch (error) {
      console.error('❌ 发布评论失败:', error);
      Alert.alert('错误', `发布评论失败: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  }, 'community-interaction');

  // 处理评论点赞
  const handleCommentLike = wrapGuestAction((commentId) => {
    setComments(prev => prev.map(comment =>
      comment.id === commentId
        ? {
          ...comment,
          isLiked: !comment.isLiked,
          likes: comment.isLiked ? comment.likes - 1 : comment.likes + 1
        }
        : comment
    ));
  }, 'community-interaction');

  if (loading || !post) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.safeArea} />
        <View style={styles.fixedHeader}>
          <TouchableOpacity style={styles.headerIcon} onPress={() => router.back()}>
            <Image source={backIcon} style={{ width: 24, height: 24, resizeMode: 'contain' }} />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>帖子详情</ThemedText>
          <View style={styles.headerIcon} />
        </View>
        <View style={styles.loadingContainer}>
          <ThemedText>{loading ? '加载中...' : '帖子不存在'}</ThemedText>
          {error && <ThemedText style={styles.errorText}>{error}</ThemedText>}
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      {/* 安全视图 */}
      <View style={styles.safeArea} />

      {/* 固定顶部标题栏 */}
      <View style={styles.fixedHeader}>
        <TouchableOpacity
          style={styles.headerIcon}
          onPress={() => router.back()}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="返回"
        >
          <Image source={backIcon} style={{ width: 24, height: 24, resizeMode: 'contain' }} />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>帖子详情</ThemedText>
        <TouchableOpacity
          style={styles.headerIcon}
          onPress={wrapGuestAction(() => {
            console.log('分享帖子:', post.id);
          }, 'community-interaction')}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="分享"
        >
          <Image source={forwardIcon} style={{ width: 24, height: 24, resizeMode: 'contain' }} />
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollContainer}
          contentContainerStyle={{ paddingTop: 120, paddingBottom: 100 }}
          showsVerticalScrollIndicator={false}
        >
          {/* 帖子内容 */}
          <View style={styles.postContainer}>
            {/* 用户信息 */}
            <View style={styles.postHeader}>
              <View style={styles.userInfo}>
                {getUserAvatar(post)}
                <View style={styles.userDetails}>
                  <ThemedText style={styles.username}>{post.user}</ThemedText>
                  <ThemedText style={styles.time}>{formatTimeFromNow(post.createTime)}</ThemedText>
                </View>
              </View>
            </View>

            {/* 帖子内容 */}
            <ThemedText style={styles.postContent}>{post.content}</ThemedText>

            {/* 媒体内容 */}
            {post.hasMedia && post.imageUrls && post.imageUrls.length > 0 && (
              <View style={styles.mediaContainer}>
                {post.imageUrls.map((imageUrl, index) => (
                  <Image
                    key={index}
                    source={{ uri: imageUrl }}
                    style={[styles.postImage, { marginBottom: index < post.imageUrls.length - 1 ? 8 : 0 }]}
                    defaultSource={require('../../assets/Community_image/PhotosOne.png')}
                  />
                ))}
              </View>
            )}

            {/* 互动按钮 */}
            <View style={styles.interactions}>
              <AnimatedLikeButton
                isLiked={false} // TODO: 需要从API获取当前用户的点赞状态
                likeCount={post.likeCount}
                onLikeToggle={async (liked) => {
                  try {
                    console.log('🔄 帖子点赞操作:', { postId: post.id, liked });

                    if (liked) {
                      await postApi.like(post.id);
                      console.log('✅ 点赞成功');
                      // 更新帖子点赞数
                      setPost(prev => prev ? {
                        ...prev,
                        likeCount: (prev.likeCount || 0) + 1
                      } : prev);
                    } else {
                      await postApi.unlike(post.id);
                      console.log('✅ 取消点赞成功');
                      // 更新帖子点赞数
                      setPost(prev => prev ? {
                        ...prev,
                        likeCount: Math.max((prev.likeCount || 0) - 1, 0)
                      } : prev);
                    }
                  } catch (error) {
                    console.error('❌ 点赞操作失败:', error);
                    throw error; // 让AnimatedLikeButton处理错误回滚
                  }
                }}
              />

              <TouchableOpacity style={styles.interactionBtn}>
                <Image source={commentIcon} style={{ width: 20, height: 20, marginRight: 4 }} />
                <ThemedText style={styles.interactionCount}>{comments.length}</ThemedText>
              </TouchableOpacity>

              <View style={{ flex: 1 }} />

              <TouchableOpacity
                style={styles.interactionBtnRight}
                onPress={wrapGuestAction(() => {
                  console.log('转发帖子:', post.id);
                }, 'community-interaction')}
              >
                <Image source={forwardIcon} style={{ width: 20, height: 20 }} />
              </TouchableOpacity>
            </View>
          </View>

          {/* 评论区标题 */}
          <View style={styles.commentsHeader}>
            <ThemedText style={styles.commentsTitle}>评论 ({comments.length})</ThemedText>
          </View>

          {/* 评论列表 */}
          {comments.map((comment) => (
            <View key={comment.id} style={styles.commentItem}>
              <View style={styles.commentHeader}>
                {getUserAvatar(comment)}
                <View style={styles.commentUserInfo}>
                  <ThemedText style={styles.commentUsername}>{comment.user}</ThemedText>
                  <ThemedText style={styles.commentTime}>{formatTimeFromNow(comment.createTime)}</ThemedText>
                </View>
              </View>

              <ThemedText style={styles.commentContent}>{comment.content}</ThemedText>

              <TouchableOpacity
                style={styles.commentLikeBtn}
                onPress={() => handleCommentLike(comment.id)}
              >
                <Image
                  source={likeIcon}
                  style={[
                    styles.commentLikeIcon,
                    comment.isLiked && styles.commentLikeIconActive
                  ]}
                />
                <ThemedText style={[
                  styles.commentLikeCount,
                  comment.isLiked && styles.commentLikeCountActive
                ]}>
                  {comment.likeCount}
                </ThemedText>
              </TouchableOpacity>
            </View>
          ))}
        </ScrollView>

        {/* 评论输入框 */}
        <View style={styles.commentInputContainer}>
          <TextInput
            style={styles.commentInput}
            placeholder="写下你的评论..."
            placeholderTextColor="#999"
            value={newComment}
            onChangeText={setNewComment}
            multiline
            maxLength={500}
          />
          <TouchableOpacity
            style={[
              styles.submitButton,
              (!newComment.trim() || isSubmitting) && styles.submitButtonDisabled
            ]}
            onPress={handleSubmitComment}
            disabled={!newComment.trim() || isSubmitting}
          >
            <ThemedText style={[
              styles.submitButtonText,
              (!newComment.trim() || isSubmitting) && styles.submitButtonTextDisabled
            ]}>
              {isSubmitting ? '发布中...' : '发布'}
            </ThemedText>
          </TouchableOpacity>
        </View>

        {/* 底部安全视图 */}
        <View style={styles.bottomSafeArea} />
      </KeyboardAvoidingView>

      {/* 游客权限提示弹窗 */}
      <GuestPermissionModal
        visible={showPermissionModal}
        onClose={closePermissionModal}
        onRegister={handleRegister}
        featureName={currentFeature.name}
        description={currentFeature.description}
      />
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF8F3'
  },
  safeArea: {
    height: 50,
    backgroundColor: '#FFF8F3',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 15,
  },
  fixedHeader: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFF8F3',
    paddingHorizontal: 16,
    borderBottomWidth: 0,
    zIndex: 10,
    height: 60,
  },
  headerIcon: {
    padding: 8
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.light.title
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: '#ff4444',
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  postContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  postHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  userDetails: {
    flex: 1,
  },
  username: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  time: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  postContent: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    marginBottom: 16,
  },
  mediaContainer: {
    marginBottom: 16,
  },
  postImage: {
    width: '100%',
    height: 250,
    borderRadius: 8,
    resizeMode: 'cover',
  },
  interactions: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  interactionBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  interactionBtnRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  interactionCount: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  // 点赞动画样式
  likeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  likeButton: {
    marginRight: 4,
  },
  likeIcon: {
    width: 20,
    height: 20,
    tintColor: '#666',
  },
  likeIconActive: {
    tintColor: '#FF69B4',
  },
  likeCount: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  likeCountActive: {
    color: '#FF69B4',
    fontWeight: 'bold',
  },
  particle: {
    position: 'absolute',
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#FF69B4',
    left: 10,
    top: 10,
  },
  // 评论区样式
  commentsHeader: {
    paddingVertical: 16,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    marginBottom: 16,
  },
  commentsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  commentItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  commentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  commentUserInfo: {
    flex: 1,
  },
  commentUsername: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  commentTime: {
    fontSize: 12,
    color: '#999',
    marginTop: 2,
  },
  commentContent: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
    marginBottom: 8,
  },
  commentLikeBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-end',
  },
  commentLikeIcon: {
    width: 16,
    height: 16,
    tintColor: '#999',
    marginRight: 4,
  },
  commentLikeIconActive: {
    tintColor: '#FF69B4',
  },
  commentLikeCount: {
    fontSize: 12,
    color: '#999',
  },
  commentLikeCountActive: {
    color: '#FF69B4',
    fontWeight: 'bold',
  },
  // 评论输入框样式
  commentInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',

  },
  commentInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 6,
    fontSize: 14,
    color: '#333',
    backgroundColor: '#f8f8f8',
    maxHeight: 80,
    marginRight: 12,
  },
  submitButton: {
    backgroundColor: '#4A90E2',
    borderRadius: 20,
    paddingHorizontal: 20,
    paddingVertical: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: '#ccc',
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  submitButtonTextDisabled: {
    color: '#999',
  },
  // 底部安全视图
  bottomSafeArea: {
    height: 34,
    backgroundColor: '#FFFFFF',
  },
});

export default Post;