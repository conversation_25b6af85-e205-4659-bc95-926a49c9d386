import React, { useState, useRef, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, Image, Dimensions, Animated, ScrollView, Text } from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import ThemedView from '../../components/ThemedView';
import ThemedText from '../../components/ThemedText';

const { width } = Dimensions.get('window');

const MusicFocus = () => {
  const router = useRouter();
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(1); // 从中间开始
  const [focusTime, setFocusTime] = useState(0); // 专注时间计时器（秒）
  const scrollViewRef = useRef(null);
  const scaleAnim = useRef(new Animated.Value(1)).current;

  // 心电图动画
  const heartbeatAnim = useRef(new Animated.Value(1)).current;
  const waveAnim = useRef(new Animated.Value(0)).current;

  // 定时器引用
  const autoSwitchTimerRef = useRef(null);
  const focusTimerRef = useRef(null);

  // 轮播图片数据
  const carouselImages = [
    require('../../assets/Community_image/MusicOne.jpg'),
    require('../../assets/Community_image/MusicTwo.jpg'),
    require('../../assets/Community_image/MusicThree.jpg'),
  ];

  // 处理播放/暂停切换
  const handlePlayPause = () => {
    const newPlayingState = !isPlaying;
    setIsPlaying(newPlayingState);
    console.log(newPlayingState ? '开始专注模式' : '暂停专注模式');
    console.log('当前图片索引:', currentIndex);
  };



  // 手动切换图片（通过点击指示器）
  const switchToImage = (index) => {
    console.log('手动切换到图片:', index);
    setCurrentIndex(index);
    if (scrollViewRef.current) {
      const itemWidth = width * 0.8 + 20; // imageContainer width + marginHorizontal * 2
      console.log('滚动到位置:', index * itemWidth);
      scrollViewRef.current.scrollTo({
        x: index * itemWidth,
        animated: true,
      });
    }
  };

  // 格式化时间显示
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 处理轮播图滚动
  const handleScroll = (event) => {
    const scrollPosition = event.nativeEvent.contentOffset.x;
    // 每个图片的宽度包括margin
    const itemWidth = width * 0.8 + 20; // imageContainer width + marginHorizontal * 2
    const index = Math.round(scrollPosition / itemWidth);

    if (index !== currentIndex && index >= 0 && index < carouselImages.length) {
      setCurrentIndex(index);

      // 中间图片放大动画
      if (index === 1) {
        Animated.spring(scaleAnim, {
          toValue: 1.1,
          useNativeDriver: true,
        }).start();
      } else {
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
        }).start();
      }
    }
  };

  // 心电图动画效果
  useEffect(() => {
    if (isPlaying) {
      // 心跳动画
      const createHeartbeat = () => {
        Animated.sequence([
          Animated.timing(heartbeatAnim, {
            toValue: 1.2,
            duration: 100,
            useNativeDriver: true,
          }),
          Animated.timing(heartbeatAnim, {
            toValue: 1,
            duration: 100,
            useNativeDriver: true,
          }),
          Animated.timing(heartbeatAnim, {
            toValue: 1.1,
            duration: 80,
            useNativeDriver: true,
          }),
          Animated.timing(heartbeatAnim, {
            toValue: 1,
            duration: 80,
            useNativeDriver: true,
          }),
          Animated.delay(800),
        ]).start(() => {
          if (isPlaying) createHeartbeat();
        });
      };

      // 波形动画
      const createWave = () => {
        Animated.loop(
          Animated.timing(waveAnim, {
            toValue: 1,
            duration: 2000,
            useNativeDriver: true,
          })
        ).start();
      };

      createHeartbeat();
      createWave();
    } else {
      heartbeatAnim.setValue(1);
      waveAnim.setValue(0);
    }
  }, [isPlaying]);

  // 自动切换图片定时器
  useEffect(() => {
    console.log('自动切换定时器状态变化:', isPlaying);
    if (isPlaying) {
      console.log('启动自动切换定时器');
      autoSwitchTimerRef.current = setInterval(() => {
        console.log('自动切换触发');
        setCurrentIndex((prevIndex) => {
          const nextIndex = (prevIndex + 1) % carouselImages.length;
          console.log('从图片', prevIndex, '切换到图片', nextIndex);

          // 滚动到下一张图片
          if (scrollViewRef.current) {
            const itemWidth = width * 0.8 + 20; // imageContainer width + marginHorizontal * 2
            console.log('自动滚动到位置:', nextIndex * itemWidth);
            scrollViewRef.current.scrollTo({
              x: nextIndex * itemWidth,
              animated: true,
            });
          }

          return nextIndex;
        });
      }, 3000); // 3秒切换一次（测试用）
    } else {
      if (autoSwitchTimerRef.current) {
        clearInterval(autoSwitchTimerRef.current);
      }
    }

    return () => {
      if (autoSwitchTimerRef.current) {
        clearInterval(autoSwitchTimerRef.current);
      }
    };
  }, [isPlaying]);

  // 专注时间计时器
  useEffect(() => {
    if (isPlaying) {
      focusTimerRef.current = setInterval(() => {
        setFocusTime((prev) => prev + 1);
      }, 1000);
    } else {
      if (focusTimerRef.current) {
        clearInterval(focusTimerRef.current);
      }
    }

    return () => {
      if (focusTimerRef.current) {
        clearInterval(focusTimerRef.current);
      }
    };
  }, [isPlaying]);

  // 初始化时设置中间图片为放大状态
  useEffect(() => {
    Animated.spring(scaleAnim, {
      toValue: 1.1,
      useNativeDriver: true,
    }).start();
  }, []);

  return (
    <ThemedView style={styles.container}>
      <LinearGradient
        colors={['#DCECF9', '#FFEEDB']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradient}
      >
        {/* 头部导航 */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
            accessible={true}
            accessibilityRole="button"
            accessibilityLabel="返回"
          >
            <Image
              source={require('../../assets/Arrows_left.png')}
              style={styles.backIcon}
            />
          </TouchableOpacity>
          <ThemedText style={styles.headerTitle}>专注模式</ThemedText>
          <View style={styles.timerContainer}>
            <Text style={styles.timerText}>{formatTime(focusTime)}</Text>
          </View>
        </View>

        {/* 轮播图容器 */}
        <View style={styles.carouselContainer}>
          <ScrollView
            ref={scrollViewRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            onScroll={handleScroll}
            scrollEventThrottle={16}
            contentContainerStyle={styles.scrollContent}
            style={styles.scrollView}
            decelerationRate="fast"
            snapToInterval={width * 0.8 + 20}
            snapToAlignment="start"
          >
            {carouselImages.map((image, index) => (
              <Animated.View
                key={index}
                style={[
                  styles.imageContainer,
                  index === 1 && { transform: [{ scale: scaleAnim }] }
                ]}
              >
                <Image source={image} style={styles.carouselImage} />
              </Animated.View>
            ))}
          </ScrollView>
        </View>

        {/* 指示器 - 可点击切换 */}
        <View style={styles.indicatorContainer}>
          {carouselImages.map((_, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.indicator,
                index === currentIndex && styles.activeIndicator
              ]}
              onPress={() => switchToImage(index)}
              accessible={true}
              accessibilityRole="button"
              accessibilityLabel={`切换到第${index + 1}张图片`}
            />
          ))}
        </View>

        {/* 心电图特效 */}
        {isPlaying && (
          <View style={styles.heartbeatContainer}>
            <View style={styles.heartbeatLine}>
              {/* 心电图波形 */}
              <Animated.View style={[styles.heartbeatWave, {
                transform: [
                  {
                    translateX: waveAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [-width, width],
                    }),
                  },
                  {
                    scaleY: heartbeatAnim,
                  },
                ],
              }]} />

              {/* 多个心电图点 */}
              {[0, 1, 2, 3, 4].map((index) => (
                <Animated.View
                  key={index}
                  style={[
                    styles.heartbeatDot,
                    {
                      left: index * 60,
                      transform: [
                        {
                          translateX: waveAnim.interpolate({
                            inputRange: [0, 1],
                            outputRange: [-width - 100, width + 100],
                          }),
                        },
                        {
                          scaleY: heartbeatAnim,
                        },
                      ],
                    },
                  ]}
                />
              ))}
            </View>

            {/* 脉冲指示器 */}
            <Animated.View
              style={[
                styles.pulseIndicator,
                {
                  transform: [{ scale: heartbeatAnim }],
                },
              ]}
            />
          </View>
        )}

        {/* 播放控制按钮 */}
        <View style={styles.controlContainer}>
          <TouchableOpacity
            style={styles.playButton}
            onPress={handlePlayPause}
            accessible={true}
            accessibilityRole="button"
            accessibilityLabel={isPlaying ? "暂停" : "播放"}
          >
            <Image
              source={isPlaying
                ? require('../../assets/Community_image/Pause.png')
                : require('../../assets/Community_image/Begin.png')
              }
              style={styles.playIcon}
            />
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  timerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 80,
  },
  timerText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    textAlign: 'center',
  },
  carouselContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    height: 300,
  },
  scrollContent: {
    alignItems: 'center',
    paddingHorizontal: width * 0.1,
  },
  imageContainer: {
    width: width * 0.8,
    height: 250,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 10,
  },
  carouselImage: {
    width: '80%',
    height: '80%',
    resizeMode: 'contain',
    borderRadius: 20,
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 30,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 4,
  },
  activeIndicator: {
    backgroundColor: '#fff',
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  controlContainer: {
    alignItems: 'center',
    paddingBottom: 60,
  },
  playButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  playIcon: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
  },
  // 心电图特效样式
  heartbeatContainer: {
    height: 60,
    marginHorizontal: 20,
    marginBottom: 20,
    justifyContent: 'center',
    position: 'relative',
    overflow: 'hidden',
  },
  heartbeatLine: {
    height: 2,
    backgroundColor: '#00ff88',
    opacity: 0.3,
    position: 'relative',
  },
  heartbeatWave: {
    position: 'absolute',
    height: 2,
    width: 100,
    backgroundColor: '#00ff88',
    top: 0,
  },
  heartbeatDot: {
    position: 'absolute',
    width: 2,
    height: 20,
    backgroundColor: '#00ff88',
    top: -9,
    borderRadius: 1,
  },
  pulseIndicator: {
    position: 'absolute',
    right: 10,
    top: -4,
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#00ff88',
    opacity: 0.8,
  },
});

export default MusicFocus;