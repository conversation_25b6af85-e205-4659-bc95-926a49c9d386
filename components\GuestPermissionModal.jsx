// components/GuestPermissionModal.jsx - 游客权限提示弹窗
import React from 'react';
import { Modal, View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { Colors } from '../constants/Colors';
import { useColorScheme } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width: screenWidth } = Dimensions.get('window');

const GuestPermissionModal = ({ 
  visible, 
  onClose, 
  onRegister, 
  featureName = "此功能",
  description = "需要注册账号才能使用完整功能"
}) => {
  const colorScheme = useColorScheme();
  const theme = Colors[colorScheme] ?? Colors.light;

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={[styles.modalContainer, { backgroundColor: theme.background }]}>
          {/* 关闭按钮 */}
          <TouchableOpacity 
            style={styles.closeButton}
            onPress={onClose}
          >
            <Ionicons name="close" size={24} color={theme.text} />
          </TouchableOpacity>

          {/* 图标 */}
          <View style={[styles.iconContainer, { backgroundColor: theme.tint + '20' }]}>
            <Ionicons name="lock-closed" size={48} color={theme.tint} />
          </View>

          {/* 标题 */}
          <Text style={[styles.title, { color: theme.text }]}>
            {featureName}需要注册
          </Text>

          {/* 描述 */}
          <Text style={[styles.description, { color: theme.text + 'CC' }]}>
            {description}
          </Text>

          {/* 功能列表 */}
          <View style={styles.featureList}>
            <View style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
              <Text style={[styles.featureText, { color: theme.text }]}>
                个性化学习计划
              </Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
              <Text style={[styles.featureText, { color: theme.text }]}>
                学习进度跟踪
              </Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
              <Text style={[styles.featureText, { color: theme.text }]}>
                社区互动功能
              </Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
              <Text style={[styles.featureText, { color: theme.text }]}>
                数据云端同步
              </Text>
            </View>
          </View>

          {/* 按钮组 */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={[styles.secondaryButton, { borderColor: theme.tint }]}
              onPress={onClose}
            >
              <Text style={[styles.secondaryButtonText, { color: theme.tint }]}>
                继续游客体验
              </Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.primaryButton, { backgroundColor: theme.tint }]}
              onPress={onRegister}
            >
              <Text style={styles.primaryButtonText}>
                立即注册
              </Text>
            </TouchableOpacity>
          </View>

          {/* 底部提示 */}
          <Text style={[styles.bottomTip, { color: theme.text + '80' }]}>
            注册完全免费，只需30秒
          </Text>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    width: Math.min(screenWidth - 40, 400),
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  featureList: {
    width: '100%',
    marginBottom: 24,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingHorizontal: 8,
  },
  featureText: {
    fontSize: 15,
    marginLeft: 12,
    flex: 1,
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
  },
  primaryButton: {
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  primaryButtonText: {
    color: 'black',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    height: 48,
    borderRadius: 12,
    borderWidth: 1.5,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  bottomTip: {
    fontSize: 13,
    textAlign: 'center',
    marginTop: 16,
  },
});

export default GuestPermissionModal;
